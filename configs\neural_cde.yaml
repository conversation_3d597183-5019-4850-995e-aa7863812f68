# Neural CDE基线模型配置文件

# 模型配置
model_type: neural_cde
hidden_dim: 128
input_dim: null  # 自动根据数据集确定
output_dim: null  # 自动根据任务确定
interpolation: cubic  # 'cubic'或'rectilinear'
use_multimodal: false
# Neural CDE特定参数
cde_embedding_dim: 64
num_cde_layers: 2
solver: 'rk4'  # 'euler', 'midpoint', 'rk4'
adjoint: false
return_sequences: true

# 插值参数
interpolation_params:
  use_time: true  # 是否在插值中包含时间维度
  spline_degree: 3  # 样条插值阶数，仅在cubic插值中有效
  smoothness: 0.1  # 插值平滑参数
  time_scaling: 1.0  # 时间缩放因子
  boundary_condition: 'not-a-knot'  # 边界条件类型: 'not-a-knot', 'natural', 'clamped'
  extrapolate: false  # 是否允许外推值

# 训练配置
batch_size: 32
num_epochs: 100
lr: 0.001
weight_decay: 0.0001
patience: 10
max_seq_len: null  # 如果需要限制序列长度，设置为整数，否则为null
seed: 42
use_early_stopping: true
use_amp: true  # 启用混合精度训练

# 数据配置
dataset: osic  # 'osic'或'adni'
data_path: data/osic  # 数据目录路径
use_images: true
task: regression  # 'regression'或'classification'，仅对ADNI数据集有效

# 输出配置
output_dir: output
use_wandb: false
wandb_project: realdiffusionnet 