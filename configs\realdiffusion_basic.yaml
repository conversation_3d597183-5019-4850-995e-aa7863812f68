# RealDiffFusionNet 基础配置文件
# 适用于初学者和快速测试

# 实验配置
experiment_name: "realdiffusion_basic"
output_dir: "output"
seed: 42

# 模型配置
model_type: realdiffusionnet
use_multimodal: true        # 启用多模态（表格+图像）

# RealDiffFusionNet 特定参数
input_channels: 6           # 输入特征维度（根据你的表格数据调整）
hidden_channels: 64         # 隐藏层维度
output_dim: 1              # 输出维度（回归任务）
attention_dim: 64          # 注意力维度
num_heads: 4               # 注意力头数
ff_dim: 128                # 前馈网络维度
num_layers: 2              # Transformer层数
dropout: 0.1               # Dropout率
fusion_type: 'concat'      # 融合策略: concat/sum/cross_attention
interpolation: 'rectilinear' # 插值方法

# 数据配置
dataset: osic
data_path: data/osic
use_images: true           # 使用DICOM图像
image_size: 224           # 图像尺寸
max_seq_len: 10           # 最大序列长度
batch_size: 4             # 批次大小（根据GPU内存调整）
num_workers: 2            # 数据加载线程数

# 图像编码器配置
image_backbone: 'efficientnet-b0'  # 图像骨干网络
image_pretrained: true             # 使用预训练权重
freeze_backbone: false             # 是否冻结骨干网络
image_embed_dim: 128              # 图像嵌入维度

# 训练配置
num_epochs: 50            # 训练轮数
learning_rate: 0.001      # 学习率
weight_decay: 0.01        # 权重衰减
scheduler: 'cosine'       # 学习率调度器
patience: 10              # 早停耐心值

# 优化器配置
optimizer: 'adamw'        # 优化器类型
beta1: 0.9               # Adam beta1
beta2: 0.999             # Adam beta2

# 损失函数配置
loss_function: 'mse'      # 损失函数类型
use_reg_loss: false       # 是否使用正则化损失
reg_weight: 0.1          # 正则化权重

# 验证配置
val_split: 0.2           # 验证集比例
test_split: 0.1          # 测试集比例
task: 'regression'       # 任务类型

# 设备配置
device: 'auto'           # 自动选择设备
use_amp: true            # 使用混合精度训练
use_wandb: false         # 是否使用wandb记录

# 保存配置
save_best_model: true    # 保存最佳模型
save_last_model: true    # 保存最后一个模型
save_predictions: true   # 保存预测结果
