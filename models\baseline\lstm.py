import torch
import torch.nn as nn

class LSTMModel(nn.Module):
    """
    LSTM模型用于疾病进展预测
    
    参数:
    input_dim (int): 输入特征维度
    hidden_dim (int): LSTM隐藏层维度
    num_layers (int): LSTM层数
    output_dim (int): 输出维度 (通常为1，表示预测值)
    dropout (float): dropout率，默认0.2
    """
    def __init__(self, input_dim, hidden_dim, num_layers=2, output_dim=1, dropout=0.2):
        super(LSTMModel, self).__init__()
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        
        # LSTM层
        self.lstm = nn.LSTM(
            input_dim, 
            hidden_dim, 
            num_layers, 
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0
        )
        
        # 输出层
        self.fc = nn.Linear(hidden_dim, output_dim)
    
    def forward(self, x, h0=None, c0=None):
        """
        前向传播
        
        参数:
        x: 输入数据，形状为 [batch_size, seq_len, input_dim]
        h0: 初始隐藏状态，如果为None则使用零初始化
        c0: 初始单元状态，如果为None则使用零初始化
        
        返回:
        输出: 预测值，形状为 [batch_size, seq_len, output_dim]
        (h_n, c_n): 最终隐藏状态和单元状态
        """
        batch_size = x.size(0)
        
        # 初始化隐藏状态和单元状态（如果未提供）
        if h0 is None or c0 is None:
            h0 = torch.zeros(self.num_layers, batch_size, self.hidden_dim).to(x.device)
            c0 = torch.zeros(self.num_layers, batch_size, self.hidden_dim).to(x.device)
        
        # LSTM前向传播
        lstm_out, (h_n, c_n) = self.lstm(x, (h0, c0))
        
        # 应用全连接层到所有时间步
        output = self.fc(lstm_out)
        
        return output, (h_n, c_n)

class LSTMModelForecaster(nn.Module):
    """
    基于LSTM的疾病进展预测模型
    
    参数:
    input_dim (int): 输入特征维度
    hidden_dim (int): LSTM隐藏层维度
    num_layers (int): LSTM层数
    output_dim (int): 输出维度 (通常为1，表示预测值)
    dropout (float): dropout率，默认0.2
    """
    def __init__(self, input_dim, hidden_dim, num_layers=2, output_dim=1, dropout=0.2):
        super(LSTMModelForecaster, self).__init__()
        
        self.lstm_model = LSTMModel(
            input_dim=input_dim,
            hidden_dim=hidden_dim,
            num_layers=num_layers,
            output_dim=output_dim,
            dropout=dropout
        )
    
    def forward(self, x, lengths=None):
        """
        前向传播
        
        参数:
        x: 输入数据，形状为 [batch_size, seq_len, input_dim]
        lengths: 每个序列的实际长度，形状为 [batch_size]，用于处理填充后的变长序列
        
        返回:
        预测值: 形状为 [batch_size, output_dim]（取每个序列最后一个有效时间步的输出）
        """
        # 使用基础LSTM模型
        output, _ = self.lstm_model(x)
        
        # 如果提供了序列长度，取每个序列的最后一个有效时间步
        if lengths is not None:
            batch_size = x.size(0)
            result = torch.zeros(batch_size, output.size(2), device=x.device)
            for i in range(batch_size):
                # 获取序列的实际长度
                seq_len = lengths[i]
                if seq_len > 0:  # 确保序列长度大于0
                    # 取该序列最后一个有效时间步的输出
                    result[i] = output[i, seq_len - 1]
            return result
        else:
            # 否则，取最后一个时间步的输出作为预测结果
            return output[:, -1, :] 