import torch
import torch.nn as nn
import torchcde

class NeuralCDE(nn.Module):
    """
    神经控制微分方程（Neural CDE）模型
    
    Neural CDE是一个基于控制微分方程的深度学习模型，用于处理不规则采样的时间序列数据。
    与普通的RNN或LSTM相比，Neural CDE能够更好地捕获时间序列的动态变化，尤其是在不规则采样的情况下。
    
    工作原理：
    1. 将离散观测数据通过插值（如立方样条插值或线性插值）转换为连续路径X(t)
    2. 定义一个隐藏状态h(t)，其动态由控制微分方程dh(t)/dt = f(h(t)) * dX(t)/dt描述
    3. 使用神经网络来参数化向量场函数f
    4. 通过数值积分求解微分方程，得到最终的隐藏状态
    5. 将隐藏状态映射到预测结果
    
    参数:
    input_channels (int): 输入特征维度（控制路径的维度）
    hidden_channels (int): 隐藏状态维度
    output_channels (int): 输出维度，默认为1（例如FVC预测）
    interpolation (str): 插值方法，'cubic'（立方样条插值）或'linear'（线性插值）
    """
    def __init__(self, input_channels, hidden_channels, output_channels=1, interpolation='cubic'):
        super(NeuralCDE, self).__init__()
        self.input_channels = input_channels
        self.hidden_channels = hidden_channels
        self.output_channels = output_channels
        self.interpolation = interpolation
        
        # 初始隐藏状态映射：将初始输入映射到隐藏空间
        # 这相当于RNN中的初始化隐藏状态
        self.initial_network = nn.Linear(input_channels, hidden_channels)
        
        # 向量场网络 (控制微分方程的函数f)
        # 这个网络接收当前的隐藏状态h(t)，输出一个矩阵，用于与dX(t)/dt相乘
        # 输出维度为hidden_channels * input_channels，因为需要将其重塑为矩阵形式
        self.func_network = nn.Sequential(
            nn.Linear(hidden_channels, 128),
            nn.Tanh(),  # Tanh激活函数可以防止梯度爆炸
            nn.Linear(128, hidden_channels * input_channels)
        )
        
        # 输出网络：将最终的隐藏状态映射到预测结果
        self.output_network = nn.Linear(hidden_channels, output_channels)
    
    def forward(self, coeffs):
        """
        前向传播
        
        参数:
        coeffs: 输入数据的插值系数，通常使用以下方法预计算：
               - torchcde.natural_cubic_spline_coeffs：用于立方样条插值
               - torchcde.linear_interpolation_coeffs：用于线性插值
        
        返回:
        输出: 预测值，形状为[batch_size, output_channels]
        """
        # 根据指定的插值方法创建连续路径X(t)
        if self.interpolation == 'cubic':
            # 使用cubic_spline插值，可以生成更平滑的路径，特别适合不规则采样数据
            X = torchcde.CubicSpline(coeffs)
        else:
            # 使用linear插值，计算成本更低，但精度可能不如立方样条插值
            X = torchcde.LinearInterpolation(coeffs)
        
        # 获取第一个时间点的值X(t_0)
        X0 = X.evaluate(X.interval[0])
        
        # 初始化隐藏状态h(t_0)
        h0 = self.initial_network(X0)
        
        # 定义向量场函数f，用于控制微分方程dh(t)/dt = f(h(t)) * dX(t)/dt
        def func(t, h):
            # 使用神经网络参数化向量场f(h(t))
            vector_field = self.func_network(h)
            
            # 重塑为矩阵形式，形状为[batch_size, hidden_channels, input_channels]
            # 这个矩阵将与dX(t)/dt相乘，控制隐藏状态的变化率
            vector_field = vector_field.view(h.size(0), self.hidden_channels, self.input_channels)
            
            # 计算路径X(t)在时间t的导数dX(t)/dt
            dXdt = X.derivative(t)
            
            # 确保dXdt的批次维度与h匹配
            if dXdt.dim() < h.dim():
                dXdt = dXdt.unsqueeze(0)
            
            # 矩阵乘法: f(h(t)) * dX(t)/dt
            # 结果形状为[batch_size, hidden_channels]，对应dh(t)/dt
            return torch.bmm(vector_field, dXdt.unsqueeze(-1)).squeeze(-1)
        
        # 使用数值积分方法求解CDE
        # cdeint在时间区间[t_0, t_N]上积分微分方程，返回整个区间的隐藏状态轨迹
        h_T = torchcde.cdeint(
            X=X,             # 控制路径
            func=func,       # 向量场函数
            z0=h0,           # 初始隐藏状态
            t=X.interval     # 积分时间区间
        )
        
        # 取最后一个时间点的隐藏状态h(t_N)
        # h_T的形状为[batch_size, num_times, hidden_channels]
        if h_T.dim() > 2:
            h_T = h_T[:, -1, :]
        else:
            h_T = h_T[-1, :]
        
        # 应用输出网络将最终隐藏状态映射到预测值
        pred_y = self.output_network(h_T)
        
        return pred_y

class NeuralCDEForecaster(nn.Module):
    """
    基于Neural CDE的疾病进展预测模型
    
    这个类封装了Neural CDE模型，添加了对输入数据的预处理和插值系数的计算，
    使其更容易用于疾病进展预测任务。
    
    参数:
    input_channels (int): 输入特征维度
    hidden_channels (int): 隐藏状态维度
    output_channels (int): 输出维度，默认为1（例如FVC或MMSE预测）
    interpolation (str): 插值方法
        - 'cubic': 立方样条插值，生成更平滑的路径
        - 'linear': 标准线性插值
        - 'rectilinear': 直线插值，特别适合阶跃型变化的数据
    """
    def __init__(self, input_channels, hidden_channels, output_channels=1, interpolation='cubic'):
        super(NeuralCDEForecaster, self).__init__()
        self.input_channels = input_channels
        self.hidden_channels = hidden_channels
        self.interpolation = interpolation
        
        # 创建Neural CDE模型
        self.neural_cde = NeuralCDE(
            input_channels=input_channels,
            hidden_channels=hidden_channels,
            output_channels=output_channels,
            interpolation=interpolation
        )
    
    def forward(self, x, lengths=None, times=None):
        """
        前向传播
        
        参数:
        x: 输入数据，形状为 [batch_size, seq_len, input_channels]
           代表每个病人在不同时间点的测量值
        lengths: 每个序列的实际长度，形状为 [batch_size]
                用于处理填充后的变长序列
        times: 时间点，形状为 [seq_len]
               如果为None则使用等间隔时间点[0,1]
        
        返回:
        预测值: 形状为 [batch_size, output_channels]，代表对下一个时间点的预测
        """
        batch_size, max_seq_len, _ = x.size()
        
        # 如果未提供时间点，则使用等间隔时间点
        # 时间点用于定义插值的控制点位置
        if times is None:
            times = torch.linspace(0, 1, max_seq_len).to(x.device)
        
        # 处理变长序列
        if lengths is not None:
            # 为每个样本分别计算插值系数
            all_coeffs = []
            for i in range(batch_size):
                # 获取实际长度
                actual_len = lengths[i].item()
                
                # 如果序列长度为1，则复制当前值，因为torchcde需要至少2个时间点
                if actual_len == 1:
                    xi = x[i, :1, :].repeat(2, 1)
                    times_i = torch.tensor([0.0, 1.0], device=x.device)
                else:
                    # 截取实际数据（去除填充）
                    xi = x[i, :actual_len, :]
                    # 截取实际时间点
                    times_i = times[:actual_len]
                
                # 根据插值方法计算系数
                if self.interpolation == 'cubic':
                    # 对于立方样条插值，确保至少有4个点
                    if xi.size(0) < 4:
                        # 如果点数不足，则退回到线性插值
                        coeff = torchcde.linear_interpolation_coeffs(xi.unsqueeze(0), times_i)
                    else:
                        coeff = torchcde.natural_cubic_spline_coeffs(xi.unsqueeze(0), times_i)
                elif self.interpolation == 'rectilinear':
                    coeff = torchcde.linear_interpolation_coeffs(xi.unsqueeze(0), times_i, rectilinear=True)
                else:
                    coeff = torchcde.linear_interpolation_coeffs(xi.unsqueeze(0), times_i)
                
                all_coeffs.append(coeff)
            
            # 单独处理每个样本
            preds = []
            for i, coeff in enumerate(all_coeffs):
                pred_i = self.neural_cde(coeff)
                preds.append(pred_i)
            
            # 组合预测结果
            pred = torch.cat(preds, dim=0)
            
        else:
            # 如果没有提供lengths信息，按照原方式处理
            # 根据插值方法计算系数
            if self.interpolation == 'cubic':
                # 使用立方样条插值（需要至少4个数据点）
                if max_seq_len < 4:
                    raise ValueError(f"立方插值需要至少4个数据点，当前序列长度为{max_seq_len}")
                coeffs = torchcde.natural_cubic_spline_coeffs(x, times)
            elif self.interpolation == 'rectilinear':
                # 使用直线插值（阶跃函数）
                coeffs = torchcde.linear_interpolation_coeffs(x, times, rectilinear=True)
            else:
                # 使用标准线性插值
                coeffs = torchcde.linear_interpolation_coeffs(x, times)
            
            # 使用Neural CDE模型进行预测
            pred = self.neural_cde(coeffs)
        
        return pred 