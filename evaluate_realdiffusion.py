#!/usr/bin/env python3
"""
RealDiffFusionNet 模型评估脚本
"""

import os
import argparse
import yaml
import torch
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from scipy.stats import pearsonr

def load_model_and_config(model_path, config_path):
    """加载模型和配置"""
    print(f"📥 加载模型: {model_path}")
    print(f"📥 加载配置: {config_path}")
    
    # 加载配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 加载模型
    checkpoint = torch.load(model_path, map_location='cpu')
    
    return checkpoint, config

def evaluate_predictions(predictions_path):
    """评估预测结果"""
    print(f"📊 分析预测结果: {predictions_path}")
    
    if not os.path.exists(predictions_path):
        print(f"❌ 预测文件不存在: {predictions_path}")
        return
    
    # 加载预测结果
    df = pd.read_csv(predictions_path)
    
    if 'y_true' not in df.columns or 'y_pred' not in df.columns:
        print("❌ 预测文件缺少必要的列 (y_true, y_pred)")
        return
    
    y_true = df['y_true'].values
    y_pred = df['y_pred'].values
    
    # 计算评估指标
    rmse = np.sqrt(mean_squared_error(y_true, y_pred))
    mae = mean_absolute_error(y_true, y_pred)
    r2 = r2_score(y_true, y_pred)
    corr, p_value = pearsonr(y_true, y_pred)
    
    print("\n📈 评估指标:")
    print(f"  RMSE: {rmse:.4f}")
    print(f"  MAE:  {mae:.4f}")
    print(f"  R²:   {r2:.4f}")
    print(f"  相关系数: {corr:.4f} (p={p_value:.4f})")
    
    # 绘制预测vs真实值散点图
    plt.figure(figsize=(10, 8))
    
    plt.subplot(2, 2, 1)
    plt.scatter(y_true, y_pred, alpha=0.6)
    plt.plot([y_true.min(), y_true.max()], [y_true.min(), y_true.max()], 'r--', lw=2)
    plt.xlabel('真实值')
    plt.ylabel('预测值')
    plt.title(f'预测 vs 真实值 (R²={r2:.3f})')
    plt.grid(True, alpha=0.3)
    
    plt.subplot(2, 2, 2)
    residuals = y_pred - y_true
    plt.scatter(y_pred, residuals, alpha=0.6)
    plt.axhline(y=0, color='r', linestyle='--')
    plt.xlabel('预测值')
    plt.ylabel('残差')
    plt.title('残差图')
    plt.grid(True, alpha=0.3)
    
    plt.subplot(2, 2, 3)
    plt.hist(residuals, bins=20, alpha=0.7, edgecolor='black')
    plt.xlabel('残差')
    plt.ylabel('频数')
    plt.title('残差分布')
    plt.grid(True, alpha=0.3)
    
    plt.subplot(2, 2, 4)
    plt.plot(y_true, label='真实值', alpha=0.7)
    plt.plot(y_pred, label='预测值', alpha=0.7)
    plt.xlabel('样本索引')
    plt.ylabel('值')
    plt.title('预测序列对比')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图像
    output_path = predictions_path.replace('.csv', '_analysis.png')
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"📊 分析图表已保存: {output_path}")
    
    plt.show()
    
    return {
        'rmse': rmse,
        'mae': mae,
        'r2': r2,
        'correlation': corr,
        'p_value': p_value
    }

def analyze_training_curves(output_dir):
    """分析训练曲线"""
    curves_path = os.path.join(output_dir, 'training_curves.png')
    
    if os.path.exists(curves_path):
        print(f"📈 训练曲线图: {curves_path}")
    else:
        print("⚠️  未找到训练曲线图")

def generate_report(model_path, config_path, predictions_path, output_dir):
    """生成评估报告"""
    print("\n📝 生成评估报告...")
    
    # 加载模型信息
    checkpoint, config = load_model_and_config(model_path, config_path)
    
    # 评估预测结果
    metrics = evaluate_predictions(predictions_path)
    
    if metrics is None:
        return
    
    # 生成报告
    report = f"""
# RealDiffFusionNet 模型评估报告

## 模型信息
- 模型类型: {config.get('model_type', 'N/A')}
- 实验名称: {config.get('experiment_name', 'N/A')}
- 多模态: {config.get('use_multimodal', False)}
- 使用图像: {config.get('use_images', False)}

## 模型架构
- 输入维度: {config.get('input_channels', 'N/A')}
- 隐藏维度: {config.get('hidden_channels', 'N/A')}
- 注意力维度: {config.get('attention_dim', 'N/A')}
- 注意力头数: {config.get('num_heads', 'N/A')}
- Transformer层数: {config.get('num_layers', 'N/A')}
- 融合策略: {config.get('fusion_type', 'N/A')}

## 训练配置
- 训练轮数: {config.get('num_epochs', 'N/A')}
- 批次大小: {config.get('batch_size', 'N/A')}
- 学习率: {config.get('learning_rate', 'N/A')}
- 优化器: {config.get('optimizer', 'N/A')}
- 损失函数: {config.get('loss_function', 'N/A')}

## 评估指标
- RMSE: {metrics['rmse']:.4f}
- MAE: {metrics['mae']:.4f}
- R²: {metrics['r2']:.4f}
- 相关系数: {metrics['correlation']:.4f}
- P值: {metrics['p_value']:.4f}

## 模型性能评价
"""
    
    # 性能评价
    if metrics['r2'] > 0.8:
        report += "🟢 **优秀**: 模型表现优秀，R²>0.8\n"
    elif metrics['r2'] > 0.6:
        report += "🟡 **良好**: 模型表现良好，R²>0.6\n"
    elif metrics['r2'] > 0.4:
        report += "🟠 **一般**: 模型表现一般，R²>0.4\n"
    else:
        report += "🔴 **较差**: 模型表现较差，需要改进\n"
    
    if metrics['correlation'] > 0.8 and metrics['p_value'] < 0.05:
        report += "🟢 **显著相关**: 预测值与真实值显著相关\n"
    elif metrics['correlation'] > 0.5:
        report += "🟡 **中等相关**: 预测值与真实值中等相关\n"
    else:
        report += "🔴 **相关性弱**: 预测值与真实值相关性较弱\n"
    
    # 保存报告
    report_path = os.path.join(output_dir, 'evaluation_report.md')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"📝 评估报告已保存: {report_path}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='RealDiffFusionNet 模型评估')
    parser.add_argument('--model', type=str, required=True, help='模型权重文件路径')
    parser.add_argument('--config', type=str, required=True, help='配置文件路径')
    parser.add_argument('--predictions', type=str, help='预测结果文件路径')
    parser.add_argument('--output_dir', type=str, default='output', help='输出目录')
    
    args = parser.parse_args()
    
    print("=" * 80)
    print("📊 RealDiffFusionNet 模型评估")
    print("=" * 80)
    
    # 检查文件存在性
    if not os.path.exists(args.model):
        print(f"❌ 模型文件不存在: {args.model}")
        return
    
    if not os.path.exists(args.config):
        print(f"❌ 配置文件不存在: {args.config}")
        return
    
    # 如果未指定预测文件，尝试自动查找
    if args.predictions is None:
        predictions_path = os.path.join(args.output_dir, 'predictions.csv')
        if os.path.exists(predictions_path):
            args.predictions = predictions_path
        else:
            print("❌ 未找到预测结果文件，请使用 --predictions 参数指定")
            return
    
    # 生成评估报告
    generate_report(args.model, args.config, args.predictions, args.output_dir)
    
    # 分析训练曲线
    analyze_training_curves(args.output_dir)
    
    print("\n" + "=" * 80)
    print("🎉 评估完成！")
    print("=" * 80)

if __name__ == "__main__":
    main()
