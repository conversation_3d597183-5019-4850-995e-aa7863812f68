# RealDiffFusionNet配置文件

# 模型配置
model_type: realdiffusionnet
hidden_dim: 128
attention_dim: 128
num_heads: 4
ff_dim: 256
num_layers: 2
dropout: 0.1
fusion_type: cross_attention  # 'concat', 'sum' 或 'cross_attention'
interpolation: rectilinear  # 'cubic'或'rectilinear'
use_multimodal: true
image_embed_dim: 128

# 训练配置
batch_size: 32
num_epochs: 100
lr: 0.001
patience: 10
max_seq_len: null  # 如果需要限制序列长度，设置为整数，否则为null
use_amp: true  # 启用混合精度训练

# 数据配置
dataset: osic  # 'osic'或'adni'
data_path: data/osic  # 数据目录路径
use_images: false  # 是否使用图像数据，测试时设为false

# 输出配置
output_dir: output
use_wandb: false
wandb_project: realdiffusionnet 