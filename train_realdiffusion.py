#!/usr/bin/env python3
"""
RealDiffFusionNet 专用训练脚本
提供更友好的训练接口和详细的进度显示
"""

import os
import sys
import argparse
import yaml
import torch
from datetime import datetime

def print_banner():
    """打印训练横幅"""
    print("=" * 80)
    print("🚀 RealDiffFusionNet 训练系统")
    print("=" * 80)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Python版本: {sys.version.split()[0]}")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"GPU设备: {torch.cuda.get_device_name(0)}")
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    print("=" * 80)

def check_data_structure(data_path):
    """检查数据结构"""
    print("\n📁 检查数据结构...")
    
    train_csv = os.path.join(data_path, "train.csv")
    test_csv = os.path.join(data_path, "test.csv")
    train_dir = os.path.join(data_path, "train")
    
    # 检查CSV文件
    if os.path.exists(train_csv):
        print(f"✅ 训练CSV文件: {train_csv}")
        import pandas as pd
        df = pd.read_csv(train_csv)
        print(f"   - 数据行数: {len(df)}")
        print(f"   - 患者数量: {df['Patient'].nunique()}")
        print(f"   - 列名: {list(df.columns)}")
    else:
        print(f"❌ 缺少训练CSV文件: {train_csv}")
        return False
    
    if os.path.exists(test_csv):
        print(f"✅ 测试CSV文件: {test_csv}")
    else:
        print(f"⚠️  缺少测试CSV文件: {test_csv}")
    
    # 检查DICOM目录
    if os.path.exists(train_dir):
        print(f"✅ 训练目录: {train_dir}")
        patient_dirs = [d for d in os.listdir(train_dir) if os.path.isdir(os.path.join(train_dir, d))]
        print(f"   - 患者目录数量: {len(patient_dirs)}")
        
        # 检查第一个患者的DICOM文件
        if patient_dirs:
            first_patient = patient_dirs[0]
            patient_path = os.path.join(train_dir, first_patient)
            dicom_files = [f for f in os.listdir(patient_path) if f.endswith('.dcm')]
            print(f"   - 示例患者 {first_patient}: {len(dicom_files)} 个DICOM文件")
    else:
        print(f"❌ 缺少训练目录: {train_dir}")
        return False
    
    return True

def load_config(config_path):
    """加载配置文件"""
    print(f"\n⚙️  加载配置文件: {config_path}")
    
    if not os.path.exists(config_path):
        print(f"❌ 配置文件不存在: {config_path}")
        return None
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    print("✅ 配置文件加载成功")
    print("📋 主要配置:")
    print(f"   - 实验名称: {config.get('experiment_name', 'N/A')}")
    print(f"   - 模型类型: {config.get('model_type', 'N/A')}")
    print(f"   - 多模态: {config.get('use_multimodal', False)}")
    print(f"   - 使用图像: {config.get('use_images', False)}")
    print(f"   - 批次大小: {config.get('batch_size', 'N/A')}")
    print(f"   - 训练轮数: {config.get('num_epochs', 'N/A')}")
    print(f"   - 学习率: {config.get('learning_rate', 'N/A')}")
    
    return config

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='RealDiffFusionNet 训练脚本')
    parser.add_argument('--config', type=str, required=True, help='配置文件路径')
    parser.add_argument('--resume', type=str, help='恢复训练的检查点路径')
    parser.add_argument('--debug', action='store_true', help='调试模式')
    
    args = parser.parse_args()
    
    # 打印横幅
    print_banner()
    
    # 加载配置
    config = load_config(args.config)
    if config is None:
        return
    
    # 检查数据结构
    data_path = config.get('data_path', 'data/osic')
    if not check_data_structure(data_path):
        print("\n❌ 数据结构检查失败，请检查数据文件")
        return
    
    # 调试模式设置
    if args.debug:
        print("\n🐛 调试模式已启用")
        config['num_epochs'] = 2
        config['batch_size'] = min(config.get('batch_size', 4), 2)
        config['use_wandb'] = False
    
    # 创建输出目录
    output_dir = config.get('output_dir', 'output')
    os.makedirs(output_dir, exist_ok=True)
    print(f"\n📂 输出目录: {output_dir}")
    
    # 开始训练
    print("\n🚀 开始训练...")
    print("=" * 80)
    
    try:
        # 导入并运行原始训练脚本
        sys.path.append('.')
        from train import main as train_main
        
        # 临时修改sys.argv以传递配置
        original_argv = sys.argv.copy()
        sys.argv = ['train.py', '--config', args.config]
        if args.resume:
            sys.argv.extend(['--resume', args.resume])
        
        # 运行训练
        train_main()
        
        # 恢复原始argv
        sys.argv = original_argv
        
        print("\n" + "=" * 80)
        print("🎉 训练完成！")
        print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
    except KeyboardInterrupt:
        print("\n⚠️  训练被用户中断")
    except Exception as e:
        print(f"\n❌ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
