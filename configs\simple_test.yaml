# 简化RealDiffFusionNet配置文件（测试用）

# 模型配置
model_type: realdiffusionnet
hidden_dim: 64
attention_dim: 64
num_heads: 2
ff_dim: 128
num_layers: 1
dropout: 0.1
fusion_type: concat
interpolation: rectilinear
use_multimodal: true
image_embed_dim: 64

# 训练配置
batch_size: 4
num_epochs: 2
lr: 0.001
patience: 3
max_seq_len: 10
use_amp: false

# 数据配置
dataset: osic
data_path: data/osic
use_images: true

# 输出配置
output_dir: output
use_wandb: false 