import torch
import torch.nn as nn

class EmbeddingFusion:
    """
    用于实现不同的融合策略的基类
    """
    @staticmethod
    def fuse(embeddings_list, fusion_type='concat'):
        """
        融合多个嵌入向量
        
        参数:
        embeddings_list: 包含多个嵌入张量的列表，每个形状为[batch_size, seq_len, embed_dim]
        fusion_type: 融合类型，'sum'或'concat'
        
        返回:
        融合后的嵌入
        """
        if fusion_type == 'sum':
            return SumFusion.fuse(embeddings_list)
        elif fusion_type == 'concat':
            return ConcatFusion.fuse(embeddings_list)
        else:
            raise ValueError(f"不支持的融合类型: {fusion_type}")

class SumFusion(EmbeddingFusion):
    """
    通过加和融合嵌入
    """
    @staticmethod
    def fuse(embeddings_list):
        """
        通过加和融合多个嵌入向量
        
        参数:
        embeddings_list: 包含多个嵌入张量的列表，每个形状为[batch_size, seq_len, embed_dim]
        
        返回:
        融合后的嵌入，形状为[batch_size, seq_len, embed_dim]
        """
        # 确保所有嵌入具有相同的维度
        embed_dims = [emb.size(-1) for emb in embeddings_list]
        
        if len(set(embed_dims)) > 1:
            raise ValueError(f"所有嵌入必须具有相同的维度，但得到了 {embed_dims}")
        
        # 执行加和融合
        fused_embedding = sum(embeddings_list)
        
        return fused_embedding

class ConcatFusion(EmbeddingFusion):
    """
    通过拼接融合嵌入
    """
    @staticmethod
    def fuse(embeddings_list):
        """
        通过拼接融合多个嵌入向量
        
        参数:
        embeddings_list: 包含多个嵌入张量的列表，每个形状为[batch_size, seq_len, embed_dim_i]
        
        返回:
        融合后的嵌入，形状为[batch_size, seq_len, sum(embed_dim_i)]
        """
        # 确保所有嵌入具有相同的batch_size和seq_len
        batch_sizes = [emb.size(0) for emb in embeddings_list]
        seq_lens = [emb.size(1) for emb in embeddings_list]
        
        if len(set(batch_sizes)) > 1 or len(set(seq_lens)) > 1:
            raise ValueError(f"所有嵌入必须具有相同的batch_size和seq_len，但得到了 batch_sizes={batch_sizes}, seq_lens={seq_lens}")
        
        # 执行拼接融合
        fused_embedding = torch.cat(embeddings_list, dim=-1)
        
        return fused_embedding

class FusionLayer(nn.Module):
    """
    用于处理多模态特征融合的层
    
    参数:
    fusion_type (str): 融合类型，'sum'或'concat'
    input_dims (list): 输入嵌入的维度列表
    output_dim (int): 输出嵌入的维度，如果为None则在拼接模式下为input_dims之和，在加和模式下为input_dims[0]
    """
    def __init__(self, fusion_type='concat', input_dims=None, output_dim=None):
        super(FusionLayer, self).__init__()
        self.fusion_type = fusion_type
        
        if fusion_type == 'concat' and output_dim is not None:
            # 对于拼接融合，如果指定了输出维度，则添加一个线性层来调整维度
            self.projection = nn.Linear(sum(input_dims), output_dim)
        elif fusion_type == 'sum' and len(set(input_dims)) > 1:
            # 对于加和融合，如果输入维度不一致，则添加投影层使维度一致
            self.projections = nn.ModuleList([
                nn.Linear(dim, input_dims[0]) if dim != input_dims[0] else nn.Identity()
                for dim in input_dims
            ])
        else:
            self.projection = nn.Identity()
            self.projections = None
    
    def forward(self, embeddings_list):
        """
        前向传播，融合多个嵌入
        
        参数:
        embeddings_list: 包含多个嵌入张量的列表
        
        返回:
        融合后的嵌入
        """
        if self.fusion_type == 'sum' and self.projections is not None:
            # 投影每个嵌入到相同的维度
            projected_embeddings = [
                proj(emb) for emb, proj in zip(embeddings_list, self.projections)
            ]
            fused_embedding = SumFusion.fuse(projected_embeddings)
        else:
            # 直接融合
            fused_embedding = EmbeddingFusion.fuse(embeddings_list, self.fusion_type)
            
        # 应用输出投影（如果有）
        if hasattr(self, 'projection') and not isinstance(self.projection, nn.Identity):
            fused_embedding = self.projection(fused_embedding)
            
        return fused_embedding 