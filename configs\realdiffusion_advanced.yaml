# RealDiffFusionNet 高级配置文件
# 适用于生产环境和高性能训练

# 实验配置
experiment_name: "realdiffusion_advanced"
output_dir: "output"
seed: 42

# 模型配置
model_type: realdiffusionnet
use_multimodal: true

# RealDiffFusionNet 高级参数
input_channels: 6
hidden_channels: 128        # 更大的隐藏维度
output_dim: 1
attention_dim: 128          # 更大的注意力维度
num_heads: 8               # 更多注意力头
ff_dim: 256                # 更大的前馈网络
num_layers: 4              # 更多Transformer层
dropout: 0.15              # 稍高的Dropout
fusion_type: 'cross_attention'  # 使用交叉注意力融合
interpolation: 'rectilinear'

# 数据配置
dataset: osic
data_path: data/osic
use_images: true
image_size: 256            # 更大的图像尺寸
max_seq_len: 15           # 更长的序列
batch_size: 8             # 更大的批次（需要更多GPU内存）
num_workers: 4            # 更多数据加载线程

# 图像编码器高级配置
image_backbone: 'efficientnet-b2'  # 更强的骨干网络
image_pretrained: true
freeze_backbone: false
image_embed_dim: 256              # 更大的图像嵌入维度
num_slices: 5                     # 使用多个DICOM切片

# 训练高级配置
num_epochs: 100           # 更多训练轮数
learning_rate: 0.0005     # 稍低的学习率
weight_decay: 0.02        # 更强的权重衰减
scheduler: 'cosine_with_restarts'  # 带重启的余弦调度
patience: 15              # 更大的早停耐心值
warmup_epochs: 5          # 学习率预热

# 优化器高级配置
optimizer: 'adamw'
beta1: 0.9
beta2: 0.999
eps: 1e-8
amsgrad: true            # 使用AMSGrad

# 损失函数高级配置
loss_function: 'huber'    # 使用Huber损失（对异常值更鲁棒）
use_reg_loss: true        # 启用正则化损失
reg_weight: 0.05         # 正则化权重
label_smoothing: 0.1     # 标签平滑

# 数据增强配置
use_augmentation: true    # 启用数据增强
rotation_range: 15       # 旋转角度范围
brightness_range: 0.2    # 亮度变化范围
contrast_range: 0.2      # 对比度变化范围
noise_std: 0.01         # 噪声标准差

# 验证配置
val_split: 0.2
test_split: 0.1
task: 'regression'
cross_validation: false   # 是否使用交叉验证
cv_folds: 5              # 交叉验证折数

# 设备配置
device: 'auto'
use_amp: true            # 混合精度训练
gradient_clip_val: 1.0   # 梯度裁剪
accumulate_grad_batches: 2  # 梯度累积

# 监控配置
use_wandb: true          # 启用wandb记录
wandb_project: "realdiffusion"
wandb_entity: "your_username"  # 替换为你的wandb用户名
log_every_n_steps: 10    # 记录频率

# 保存配置
save_best_model: true
save_last_model: true
save_predictions: true
save_attention_weights: true  # 保存注意力权重
checkpoint_every_n_epochs: 10  # 定期保存检查点

# 预训练配置（可选）
pretrained_cde_path: null     # 预训练CDE模型路径
freeze_cde: false            # 是否冻结CDE部分
pretrained_image_path: null   # 预训练图像编码器路径
