#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试环境和数据设置是否正确
"""

import os
import sys
import torch
import numpy as np
import pandas as pd
import yaml
from utils.data_preprocessing import create_dataloaders

def test_gpu():
    """测试GPU是否可用"""
    print("=" * 50)
    print("测试 GPU 可用性:")
    print("-" * 50)
    
    if torch.cuda.is_available():
        device_count = torch.cuda.device_count()
        print(f"✓ GPU可用! 检测到 {device_count} 个GPU设备")
        for i in range(device_count):
            print(f"  - GPU {i}: {torch.cuda.get_device_name(i)}")
        print(f"  - 当前GPU内存使用: {torch.cuda.memory_allocated(0) / 1024**2:.2f} MB")
        print(f"  - 当前GPU内存缓存: {torch.cuda.memory_reserved(0) / 1024**2:.2f} MB")
    else:
        print("✗ 未检测到GPU。模型将使用CPU运行，这可能会很慢。")
    
    print("=" * 50)

def test_data(dataset_name, data_path):
    """测试数据是否可用"""
    print(f"\n测试 {dataset_name.upper()} 数据集:")
    print("-" * 50)
    
    # 检查数据目录
    if not os.path.exists(data_path):
        print(f"✗ 数据目录不存在: {data_path}")
        return False
    
    if dataset_name == 'osic':
        # 检查OSIC数据文件
        train_path = os.path.join(data_path, 'train.csv')
        test_path = os.path.join(data_path, 'test.csv')
        
        if not os.path.exists(train_path):
            print(f"✗ 训练数据文件不存在: {train_path}")
            return False
        
        if not os.path.exists(test_path):
            print(f"✗ 测试数据文件不存在: {test_path}")
            return False
        
        # 加载数据
        try:
            train_df = pd.read_csv(train_path)
            test_df = pd.read_csv(test_path)
            print(f"✓ 成功加载OSIC数据集")
            print(f"  - 训练数据: {len(train_df)}行 x {len(train_df.columns)}列")
            print(f"  - 测试数据: {len(test_df)}行 x {len(test_df.columns)}列")
            print(f"  - 患者数量: {train_df['Patient'].nunique()}")
        except Exception as e:
            print(f"✗ 加载数据时出错: {e}")
            return False
    
    elif dataset_name == 'adni':
        # 检查ADNI数据文件
        adni_path = os.path.join(data_path, 'ADNIMERGE.csv')
        
        if not os.path.exists(adni_path):
            print(f"✗ ADNI数据文件不存在: {adni_path}")
            return False
        
        # 加载数据
        try:
            adni_df = pd.read_csv(adni_path)
            print(f"✓ 成功加载ADNI数据集")
            print(f"  - 数据: {len(adni_df)}行 x {len(adni_df.columns)}列")
            print(f"  - 患者数量: {adni_df['RID'].nunique()}")
        except Exception as e:
            print(f"✗ 加载数据时出错: {e}")
            return False
    
    else:
        print(f"✗ 未知数据集: {dataset_name}")
        return False
    
    return True

def test_dataloaders(config_path):
    """测试数据加载器是否可用"""
    print("\n测试数据加载器:")
    print("-" * 50)
    
    # 加载配置
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        print(f"✓ 成功加载配置文件: {config_path}")
    except Exception as e:
        print(f"✗ 加载配置文件时出错: {e}")
        return False
    
    # 创建数据加载器
    try:
        dataset_name = config.get('dataset', 'osic')
        data_path = config.get('data_path', f'data/{dataset_name}')
        batch_size = config.get('batch_size', 32)
        use_images = config.get('use_images', False)
        max_seq_len = config.get('max_seq_len')
        task = config.get('task', 'regression')
        
        train_loader, val_loader, test_loader = create_dataloaders(
            dataset_name=dataset_name,
            data_dir=data_path,
            batch_size=batch_size,
            use_images=use_images,
            max_seq_len=max_seq_len,
            task=task
        )
        
        print(f"✓ 成功创建数据加载器")
        print(f"  - 训练集样本数: {len(train_loader.dataset)}")
        print(f"  - 验证集样本数: {len(val_loader.dataset)}")
        print(f"  - 测试集样本数: {len(test_loader.dataset) if test_loader else 0}")
        
        # 获取一个批次的数据
        batch = next(iter(train_loader))
        print(f"  - 批次大小: {batch['features'].size(0)}")
        print(f"  - 特征形状: {batch['features'].shape}")
        print(f"  - 目标形状: {batch['target'].shape}")
        
        return True
    except Exception as e:
        print(f"✗ 创建数据加载器时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("\n" + "=" * 50)
    print("RealDiffFusionNet 环境和数据测试")
    print("=" * 50)
    
    # 测试GPU
    test_gpu()
    
    # 测试OSIC数据集
    osic_success = test_data('osic', 'data/osic')
    
    # 测试ADNI数据集
    adni_success = test_data('adni', 'data/adni')
    
    # 如果数据测试成功，测试数据加载器
    if osic_success:
        print("\n测试OSIC数据加载器:")
        try:
            test_dataloaders('configs/realdiffusionnet.yaml')
        except Exception as e:
            print(f"✗ 测试OSIC数据加载器时出错: {e}")
    
    if adni_success:
        print("\n测试ADNI数据加载器:")
        try:
            test_dataloaders('configs/adni.yaml')
        except Exception as e:
            print(f"✗ 测试ADNI数据加载器时出错: {e}")
    
    print("\n" + "=" * 50)
    print("测试完成!")
    print("=" * 50)

if __name__ == "__main__":
    main() 