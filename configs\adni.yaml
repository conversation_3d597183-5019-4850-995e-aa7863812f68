# ADNI数据集的RealDiffusionNet模型配置文件

# 模型配置
model_type: realdiffusionnet
hidden_dim: 128
input_dim: null  # 自动根据数据集确定
output_dim: null  # 自动根据任务确定
interpolation: cubic  # 'cubic'或'rectilinear'
use_multimodal: true
fusion_type: cross_attention  # 'concat'、'sum'或'cross_attention'

# 注意力配置
attention_dim: 128
num_heads: 4
ff_dim: 256
num_layers: 2
dropout: 0.1

# Neural CDE特定参数
cde_embedding_dim: 64
num_cde_layers: 2
solver: 'rk4'  # 'euler', 'midpoint', 'rk4'
adjoint: false
return_sequences: true

# 图像模型配置
image_backbone: resnet18  # 'resnet18', 'resnet50', 'efficientnet_b0'
image_pretrained: true
freeze_backbone: true

# 训练配置
batch_size: 16
num_epochs: 150
lr: 0.0005
weight_decay: 0.0001
patience: 15
max_seq_len: null  # 如果需要限制序列长度，设置为整数，否则为null
seed: 42
use_early_stopping: true

# 数据配置
dataset: adni
data_path: data/adni
use_images: false  # 修改为false以便测试
task: regression  # 'regression'或'classification'

# MRI配置
mri:
  slice_selection: "center_three"  # 'center_three', 'uniform_sample', 'center_only'
  normalization: "minmax"  # 'minmax', 'zscore', 'percentile'
  augmentation: false  # 是否使用数据增强
  augmentation_params:
    rotation_range: 10  # 旋转角度范围
    width_shift_range: 0.1  # 水平平移范围
    height_shift_range: 0.1  # 垂直平移范围
    zoom_range: 0.1  # 缩放范围
    horizontal_flip: true  # 是否水平翻转
  preprocessing:
    resize: [224, 224]  # 调整大小
    intensity_clipping: true  # 是否进行强度裁剪
    percentile_range: [1, 99]  # 强度裁剪百分位范围

# 任务配置
# regression: 预测认知评分（MMSE）
# classification: 预测诊断状态（CN/MCI/AD）
target_column: MMSE  # 对于回归任务使用'MMSE'，对于分类任务使用'DX'

# 分类任务特有配置
classification:
  num_classes: 3  # 0: CN, 1: MCI, 2: AD
  class_weights: [1.0, 1.0, 1.0]  # 用于处理类别不平衡
  focal_loss_gamma: 2.0  # Focal Loss的gamma参数，用于处理难样本
  metrics:
    - accuracy
    - precision
    - recall
    - f1
    - confusion_matrix
    - auc
    - kappa

# 回归任务特有配置
regression:
  loss: 'mse'  # 'mse', 'mae', 'huber'
  metrics:
    - rmse
    - mae
    - r2
    - correlation

# 输出配置
output_dir: output/adni
use_wandb: false
wandb_project: realdiffusionnet
wandb_group: adni

# 评估配置
eval_horizon_steps: 4  # 预测未来时间点的数量
plot_trajectories: true  # 是否绘制患者轨迹图
save_predictions: true  # 是否保存预测结果
visualization:
  plot_confusion_matrix: true  # 是否绘制混淆矩阵（仅分类任务）
  plot_feature_importance: true  # 是否绘制特征重要性
  plot_attention_weights: true  # 是否绘制注意力权重
  save_individual_predictions: true  # 是否保存每个患者的预测结果

# 模型保存与加载配置
checkpointing:
  save_best_only: true  # 是否只保存最佳模型
  save_freq: 10  # 每多少个epoch保存一次模型
  load_pretrained: false  # 是否加载预训练模型
  pretrained_path: null  # 预训练模型路径 