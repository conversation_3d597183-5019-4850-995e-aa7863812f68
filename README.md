# RealDiffusionNet: 实时疾病进展预测的多模态融合

RealDiffusionNet是一个基于神经控制微分方程（Neural CDE）和多头注意力的框架，用于疾病进展预测任务。该框架能够处理时间序列数据和图像等多模态输入，适用于各种医疗预测任务。

## 主要特点

- **神经控制微分方程**：基于控制微分方程的深度学习模型，能够处理不规则采样的时间序列数据
- **多模态融合**：支持结构化数据和图像数据的融合，包括拼接融合和加和融合策略
- **多头注意力机制**：使用注意力机制捕捉时间序列中的长期依赖关系
- **多数据集支持**：同时支持OSIC肺纤维化数据集和ADNI阿尔茨海默病数据集
- **灵活的任务设定**：支持回归任务（如FVC、MMSE评分预测）和分类任务（如疾病状态预测）

## 最近优化更新

### 2023年7月版本优化

1. **ADNI数据集支持完善**：
   - 完整实现ADNIDataset类和数据预处理逻辑
   - 支持回归任务（认知评分预测）和分类任务（疾病状态预测）
   - 改进MRI图像处理流程，支持多切片提取

2. **多模态融合策略增强**：
   - 新增加和融合（Sum Fusion）策略
   - 实现CrossModalAttentionFusion跨模态注意力融合
   - 支持不同维度的特征对齐和投影

3. **配置文件完善**：
   - 扩展Neural CDE配置文件，添加更全面的CDE超参数
   - 新增ADNI数据集专用配置文件
   - 提供详细的参数注释和默认值

4. **评估模块增强**：
   - 完整实现核心评估指标（RMSE、MAE、R²等）
   - 新增预测未来时间点的评估函数
   - 添加患者轨迹可视化功能
   - 支持分类指标评估（准确率、精确率、召回率、F1值）

5. **文档与代码注释**：
   - 为Neural CDE实现添加详细注释，解释工作原理
   - 新增训练参数详细说明文档
   - 改进代码可读性和维护性

## 使用方法

### 安装依赖

```bash
pip install -r requirements.txt
```

### 数据准备

数据集应组织在以下结构：

```
data/
├── osic/
│   ├── train.csv
│   ├── test.csv
│   └── train/
│       └── [patient_folders]/
├── adni/
│   ├── ADNIMERGE.csv
│   └── MRI/
│       └── [patient_folders]/
```

### 训练模型

```bash
# 训练OSIC数据集的RealDiffusionNet模型
python train.py --config configs/realdiffusionnet.yaml --dataset osic --data_path data/osic

# 训练ADNI数据集的回归模型
python train.py --config configs/adni.yaml --task regression --target_column MMSE

# 训练ADNI数据集的分类模型
python train.py --config configs/adni.yaml --task classification --target_column DX

# 使用Neural CDE基线模型
python train.py --config configs/neural_cde.yaml --dataset osic
```

### 评估模型

```bash
python evaluate.py --config configs/realdiffusionnet.yaml --checkpoint path/to/model.pth --dataset osic
```

### 生成预测

```bash
python predict.py --config configs/realdiffusionnet.yaml --checkpoint path/to/model.pth --input path/to/input_data
```

## 引用

如果您在研究中使用了本代码，请引用以下论文：

```
@article{wang2023realdiffusionnet,
  title={RealDiffusionNet: Real-time Disease Progression Modeling via Diffusion-based Multimodal Fusion},
  author={Wang, J. and Zhang, L. and et al.},
  journal={arXiv preprint arXiv:2301.xxxx},
  year={2023}
}
```

## 许可证

MIT License 