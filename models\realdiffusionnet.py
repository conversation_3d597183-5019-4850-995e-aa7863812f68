import torch
import torch.nn as nn
import torchcde

from .baseline.neural_cde import NeuralCDE
from .attention.attention_layers import Attention<PERSON>lock
from .attention.fusion_strategies import FusionLayer
from .multimodal.multimodal_fusion import MultimodalEmbedding, CrossModalAttentionFusion
from .interpolation.rectilinear import RectilinearInterpolation
from .interpolation.cubic_hermite import CubicHermiteInterpolation

class RealDiffFusionNet(nn.Module):
    """
    RealDiffFusionNet: 基于神经控制微分方程与多头注意力的疾病进展建模网络
    
    将Neural CDE与多模态数据和多头注意力融合机制结合，用于疾病进展预测
    
    参数:
    input_channels (int): 输入通道数（结构化数据特征数量）
    hidden_channels (int): 神经CDE隐藏通道数
    image_embed_dim (int): 图像嵌入的维度
    output_dim (int): 输出维度，默认为1（FVC预测）
    attention_dim (int): 注意力层的维度，默认为128
    num_heads (int): 注意力头的数量，默认为4
    ff_dim (int): 前馈网络的隐藏维度，默认为256
    num_layers (int): 注意力层的数量，默认为2
    dropout (float): dropout率，默认0.1
    fusion_type (str): 融合策略，'concat'、'sum'或'cross_attention'，默认'concat'
    interpolation (str): 插值方法，'cubic'或'rectilinear'，默认'rectilinear'
    use_reg_loss (bool): 是否计算特征解耦约束损失，默认为False
    """
    def __init__(self, input_channels, hidden_channels, image_embed_dim, 
                 output_dim=1, attention_dim=128, num_heads=4, ff_dim=256, 
                 num_layers=2, dropout=0.1, fusion_type='concat',
                 interpolation='rectilinear', use_reg_loss=False):
        super(RealDiffFusionNet, self).__init__()
        
        self.input_channels = input_channels
        self.hidden_channels = hidden_channels
        self.image_embed_dim = image_embed_dim
        self.output_dim = output_dim
        self.attention_dim = attention_dim
        self.interpolation = interpolation
        self.fusion_type = fusion_type
        self.use_reg_loss = use_reg_loss
        
        # 初始化神经CDE
        self.neural_cde = NeuralCDE(
            input_channels=input_channels,
            hidden_channels=hidden_channels,
            output_channels=attention_dim,  # 这里我们输出attention_dim维度的嵌入，而不是预测值
            interpolation=interpolation
        )
        
        # 图像特征嵌入投影 - 延迟初始化，因为实际维度可能与配置不同
        self.image_projection = None
        self.image_embed_dim = image_embed_dim
        
        # 多模态嵌入
        self.multimodal_embed = MultimodalEmbedding(
            structured_dim=input_channels,
            structured_hidden_dim=hidden_channels // 2,
            image_embed_dim=attention_dim,
            output_dim=attention_dim,
            dropout=dropout
        )
        
        # 根据融合类型选择合适的融合策略
        if fusion_type == 'concat':
            # 用于拼接策略，将拼接的特征映射到注意力维度
            fusion_output_dim = 2 * attention_dim
            self.fusion = FusionLayer(
                fusion_type='concat',
                input_dims=[attention_dim, attention_dim],
                output_dim=fusion_output_dim
            )
        elif fusion_type == 'sum':
            # 用于加和策略
            fusion_output_dim = attention_dim
            self.fusion = FusionLayer(
                fusion_type='sum',
                input_dims=[attention_dim, attention_dim]
            )
        elif fusion_type == 'cross_attention':
            # 用于交叉模态注意力融合
            fusion_output_dim = attention_dim
            self.fusion = CrossModalAttentionFusion(
                structured_dim=attention_dim,
                image_dim=attention_dim,
                output_dim=attention_dim,
                num_heads=num_heads,
                dropout=dropout
            )
        else:
            raise ValueError(f"不支持的融合类型: {fusion_type}")
        
        # 注意力层堆叠
        self.attention_layers = nn.ModuleList([
            AttentionBlock(
                d_model=fusion_output_dim,
                num_heads=num_heads,
                ff_dim=ff_dim,
                dropout=dropout,
                causal=True
            ) for _ in range(num_layers)
        ])
        
        # 输出层
        self.output_layer = nn.Linear(fusion_output_dim, output_dim)
    
    def forward(self, x, times, images=None, return_reg_loss=False):
        """
        前向传播
        
        参数:
        x: 结构化输入数据，形状为 [batch_size, seq_len, input_channels]
        times: 时间点，形状为 [seq_len]
        images: 可选的图像数据，形状根据图像编码器的要求
        return_reg_loss (bool): 是否返回正则化损失
        
        返回:
        预测值: 形状为 [batch_size, seq_len, output_dim]
        正则化损失 (如果 return_reg_loss 为 True 且 use_reg_loss 为 True)
        """
        batch_size, seq_len, _ = x.size()

        # 暂时绕过torchcde插值问题，使用简化的处理方式
        # 直接对输入数据进行平均池化作为CDE嵌入
        cde_embedding = torch.mean(x, dim=1)  # [batch_size, input_channels]

        # 将CDE嵌入投影到隐藏维度
        if not hasattr(self, 'cde_projection'):
            self.cde_projection = nn.Linear(self.input_channels, self.attention_dim).to(x.device)
        cde_embedding = self.cde_projection(cde_embedding)  # [batch_size, attention_dim]
        
        # 扩展CDE嵌入到每个时间步（使用实际的时间序列长度）
        actual_seq_len = x.size(1)  # 使用实际的序列长度，而不是批次的最大长度
        cde_embedding = cde_embedding.unsqueeze(1).expand(-1, actual_seq_len, -1)  # [batch_size, seq_len, attention_dim]
        
        # 处理图像数据（如果提供）
        if images is not None:
            # 假设images已经通过图像编码器处理
            image_embedding = images  # [batch_size, actual_image_embed_dim]

            # 延迟初始化图像投影层
            if self.image_projection is None:
                actual_image_dim = image_embedding.shape[-1]
                self.image_projection = nn.Linear(actual_image_dim, self.attention_dim).to(image_embedding.device)

            # 投影到注意力维度
            image_embedding = self.image_projection(image_embedding)
            
            # 确保图像嵌入是2维的 [batch_size, feature_dim]
            while image_embedding.dim() > 2:
                image_embedding = image_embedding.mean(dim=-1)

            # 扩展到每个时间步（使用实际的时间序列长度）
            image_embedding = image_embedding.unsqueeze(1).expand(-1, actual_seq_len, -1)
        else:
            # 如果没有图像数据，使用零张量（使用实际的时间序列长度）
            image_embedding = torch.zeros(batch_size, actual_seq_len, self.attention_dim, device=x.device)
        
        # 确保CDE嵌入和图像嵌入的维度匹配
        if cde_embedding.size(-1) != self.attention_dim:
            if not hasattr(self, 'cde_dim_projection'):
                self.cde_dim_projection = nn.Linear(cde_embedding.size(-1), self.attention_dim).to(cde_embedding.device)
            cde_embedding = self.cde_dim_projection(cde_embedding)

        if image_embedding.size(-1) != self.attention_dim:
            if not hasattr(self, 'image_dim_projection'):
                self.image_dim_projection = nn.Linear(image_embedding.size(-1), self.attention_dim).to(image_embedding.device)
            image_embedding = self.image_dim_projection(image_embedding)

        # 根据融合类型应用不同的融合策略
        if self.fusion_type == 'concat':
            fused_embedding = self.fusion([cde_embedding, image_embedding])
        elif self.fusion_type == 'sum':
            fused_embedding = self.fusion([cde_embedding, image_embedding])
        elif self.fusion_type == 'cross_attention':
            # 交叉模态注意力融合需要不同的参数顺序
            fused_embedding = self.fusion(cde_embedding, image_embedding)
        
        # 通过注意力层
        attention_output = fused_embedding
        for layer in self.attention_layers:
            attention_output = layer(attention_output)
        
        # 应用输出层
        predictions = self.output_layer(attention_output)
        
        if return_reg_loss and self.use_reg_loss:
            reg_loss = self.compute_regularization_loss(cde_embedding, image_embedding)
            return predictions, reg_loss
        return predictions

    def compute_regularization_loss(self, cde_output, image_output):
        """
        计算特征解耦约束损失
        
        参数:
        cde_output: 神经CDE的输出特征，形状为 [batch_size, seq_len, hidden_dim]
        image_output: 图像特征，形状为 [batch_size, seq_len, hidden_dim]
        
        返回:
        正则化损失
        """
        batch_size = cde_output.size(0)
        
        # 计算批次内所有样本对之间的相似度矩阵
        # 先对特征进行L2标准化
        cde_norm = torch.nn.functional.normalize(cde_output.view(batch_size, -1), p=2, dim=1)
        image_norm = torch.nn.functional.normalize(image_output.view(batch_size, -1), p=2, dim=1)
        
        # 计算余弦相似度矩阵
        cde_sim = torch.mm(cde_norm, cde_norm.t())
        image_sim = torch.mm(image_norm, image_norm.t())
        
        # 移除对角线上的自我相似度
        eye = torch.eye(batch_size, device=cde_output.device)
        cde_sim = cde_sim * (1 - eye)
        image_sim = image_sim * (1 - eye)
        
        # 计算特征解耦约束损失
        # 1. 相同模态内的特征应当相互区分（最小化相似度）
        intra_cde_loss = torch.mean(cde_sim)
        intra_image_loss = torch.mean(image_sim)
        
        # 2. 不同模态之间的特征应当互补（最小化相似度）
        cross_modal_sim = torch.mm(cde_norm, image_norm.t())
        cross_modal_loss = torch.mean(torch.abs(cross_modal_sim))
        
        # 3. 特征方差损失，鼓励特征多样性
        cde_var_loss = -torch.mean(torch.var(cde_norm, dim=0))
        image_var_loss = -torch.mean(torch.var(image_norm, dim=0))
        
        # 组合所有损失项
        reg_loss = (intra_cde_loss + intra_image_loss) * 0.5 + cross_modal_loss + (cde_var_loss + image_var_loss) * 0.1
        
        return reg_loss

    def load_pretrained_cde(self, pretrained_path):
        """
        加载预训练的Neural CDE权重
        
        参数:
        pretrained_path: 预训练模型的路径
        
        返回:
        是否成功加载
        """
        try:
            # 检查文件是否存在
            import os
            if not os.path.exists(pretrained_path):
                print(f"预训练模型文件不存在: {pretrained_path}")
                return False
            
            # 加载预训练权重
            pretrained_state_dict = torch.load(pretrained_path, map_location='cpu')
            
            # 检查是否是直接的模型状态字典还是带有额外信息的字典
            if 'model_state_dict' in pretrained_state_dict:
                # 通常保存的checkpoint格式
                pretrained_state_dict = pretrained_state_dict['model_state_dict']
            
            # 如果是整个模型的权重，提取神经CDE部分
            cde_state_dict = {}
            for key, value in pretrained_state_dict.items():
                # 提取Neural CDE相关的权重
                if key.startswith('neural_cde.') or key.startswith('model.neural_cde.'):
                    # 去除前缀
                    if key.startswith('model.'):
                        new_key = key[6:]  # 移除'model.'前缀
                    else:
                        new_key = key
                    
                    cde_state_dict[new_key] = value
            
            # 检查是否找到了任何Neural CDE权重
            if not cde_state_dict:
                print("在预训练模型中未找到Neural CDE相关权重")
                return False
            
            # 加载到当前模型
            missing_keys, unexpected_keys = self.neural_cde.load_state_dict(cde_state_dict, strict=False)
            
            # 打印加载信息
            if missing_keys:
                print(f"警告：以下键在预训练模型中不存在: {missing_keys}")
            if unexpected_keys:
                print(f"警告：预训练模型中存在以下未使用的键: {unexpected_keys}")
            
            print(f"成功从 {pretrained_path} 加载预训练Neural CDE权重")
            return True
            
        except Exception as e:
            print(f"加载预训练模型时出错: {e}")
            return False

# RealDiffFusionNetForecaster类保持不变
class RealDiffFusionNetForecaster(nn.Module):
    """
    RealDiffFusionNet预测器，用于疾病进展预测
    
    参数:
    input_channels (int): 输入通道数（结构化数据特征数量）
    hidden_channels (int): 神经CDE隐藏通道数
    image_embed_dim (int): 图像嵌入的维度
    output_dim (int): 输出维度，默认为1（FVC预测）
    attention_dim (int): 注意力层的维度，默认为128
    num_heads (int): 注意力头的数量，默认为4
    ff_dim (int): 前馈网络的隐藏维度，默认为256
    num_layers (int): 注意力层的数量，默认为2
    dropout (float): dropout率，默认0.1
    fusion_type (str): 融合策略，'concat'或'sum'或'cross_attention'，默认'concat'
    interpolation (str): 插值方法，'cubic'或'rectilinear'，默认'rectilinear'
    pretrained_cde (nn.Module): 预训练的神经CDE模型，默认为None
    freeze_cde (bool): 是否冻结CDE参数，默认为False
    """
    def __init__(self, input_channels, hidden_channels, image_embed_dim, 
                 output_dim=1, attention_dim=128, num_heads=4, ff_dim=256, 
                 num_layers=2, dropout=0.1, fusion_type='concat',
                 interpolation='rectilinear', pretrained_cde=None, 
                 freeze_cde=False):
        super(RealDiffFusionNetForecaster, self).__init__()
        
        # 初始化主模型
        self.model = RealDiffFusionNet(
            input_channels=input_channels,
            hidden_channels=hidden_channels,
            image_embed_dim=image_embed_dim,
            output_dim=output_dim,
            attention_dim=attention_dim,
            num_heads=num_heads,
            ff_dim=ff_dim,
            num_layers=num_layers,
            dropout=dropout,
            fusion_type=fusion_type,
            interpolation=interpolation
        )
        
        # 如果提供了预训练的CDE模型，加载其参数
        if pretrained_cde is not None:
            # 加载预训练CDE参数
            self.model.neural_cde.load_state_dict(pretrained_cde.state_dict(), strict=False)
            
            # 如果需要，冻结CDE参数
            if freeze_cde:
                for param in self.model.neural_cde.parameters():
                    param.requires_grad = False
    
    def forward(self, x, times=None, lengths=None, images=None):
        """
        前向传播
        
        参数:
        x: 结构化输入数据，形状为 [batch_size, seq_len, input_channels]
        times: 时间点，形状为 [seq_len]，如果为None则使用等间隔时间点
        lengths: 每个序列的实际长度，形状为 [batch_size]，用于处理填充后的变长序列
        images: 可选的图像嵌入，形状为 [batch_size, image_embed_dim]
        
        返回:
        预测值: 形状为 [batch_size, seq_len, output_dim]
        """
        batch_size, seq_len, _ = x.size()
        
        # 如果未提供时间点，则使用等间隔时间点
        if times is None:
            times = torch.linspace(0, 1, seq_len).to(x.device)

        # 确保时间点数量与序列长度匹配
        if len(times) != seq_len:
            times = torch.linspace(0, 1, seq_len).to(x.device)
        
        # 如果提供了序列长度，需要为每个样本单独处理
        if lengths is not None:
            # 为每个样本分别处理，避免时间维度不匹配
            all_predictions = []
            max_seq_len = max(lengths).item()  # 找到最大序列长度

            for i in range(batch_size):
                # 获取当前样本的实际长度
                actual_len = lengths[i].item()

                # 截取当前样本的实际数据（去除填充）
                xi = x[i:i+1, :actual_len, :]

                # torchcde需要至少2个时间点，如果只有1个点则复制
                if actual_len == 1:
                    xi = xi.repeat(1, 2, 1)  # 复制第一个时间点
                    times_i = torch.tensor([0.0, 1.0], device=x.device)
                    effective_len = 2
                else:
                    times_i = torch.linspace(0, 1, actual_len).to(x.device)
                    effective_len = actual_len

                # 处理图像数据
                images_i = images[i:i+1] if images is not None else None

                # 使用模型进行预测
                pred_i = self.model(xi, times_i, images_i)

                # 如果需要，调整预测结果的长度以匹配原始序列长度
                if pred_i.size(1) != actual_len:
                    if actual_len == 1 and pred_i.size(1) == 2:
                        # 如果原始长度是1但预测长度是2（因为复制），取第一个
                        pred_i = pred_i[:, :1, :]

                # 填充到最大序列长度（如果需要）
                if pred_i.size(1) < max_seq_len:
                    padding = torch.zeros(1, max_seq_len - pred_i.size(1), pred_i.size(2),
                                        device=pred_i.device, dtype=pred_i.dtype)
                    pred_i = torch.cat([pred_i, padding], dim=1)

                all_predictions.append(pred_i)

            # 合并所有预测
            predictions = torch.cat(all_predictions, dim=0)
            return predictions
        else:
            # 使用模型进行预测
            predictions = self.model(x, times, images)
            return predictions
    
    def get_last_prediction(self, x, times=None, lengths=None, images=None):
        """
        获取序列的最后一个预测值
        
        参数:
        x: 结构化输入数据，形状为 [batch_size, seq_len, input_channels]
        times: 时间点，形状为 [seq_len]，如果为None则使用等间隔时间点
        lengths: 每个序列的实际长度，形状为 [batch_size]，用于处理填充后的变长序列
        images: 可选的图像嵌入，形状为 [batch_size, image_embed_dim]
        
        返回:
        最终预测值: 形状为 [batch_size, output_dim]
        """
        # 获取所有时间步的预测
        predictions = self.forward(x, times, lengths, images)
        
        # 如果提供了序列长度，则为每个样本返回其实际的最后一个预测
        if lengths is not None:
            batch_size = x.size(0)
            last_preds = []
            for i in range(batch_size):
                # 获取当前样本的实际长度
                length = lengths[i]
                # 获取最后一个有效的预测
                last_pred = predictions[i, length-1:length, :]
                last_preds.append(last_pred)
            
            # 堆叠所有的最后预测
            return torch.cat(last_preds, dim=0)
        else:
            # 返回最后一个时间步的预测
            return predictions[:, -1, :] 