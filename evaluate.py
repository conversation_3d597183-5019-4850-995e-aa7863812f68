import os
import torch
import torch.nn as nn
import numpy as np
import argparse
import yaml
import pandas as pd

from models.baseline.lstm import LSTMModelForecaster
from models.baseline.neural_cde import NeuralCDEForecaster
from models.multimodal.image_models import DICOMSliceEncoder
from models.realdiffusionnet import RealDiffFusionNetForecaster
from utils.data_preprocessing import create_dataloaders
from utils.evaluation import evaluate_model
from utils.visualization import (
    plot_predictions, plot_metrics, plot_disease_progression, 
    plot_model_comparison, plot_attention_weights
)

def load_model(model_path, model_type, input_dim, config):
    """
    加载预训练模型
    
    参数:
    model_path: 模型文件路径
    model_type: 模型类型
    input_dim: 输入特征维度
    config: 配置字典
    
    返回:
    加载的模型
    """
    model = None
    
    # 创建模型实例
    if model_type == 'lstm':
        model = LSTMModelForecaster(
            input_dim=input_dim,
            hidden_dim=config.get('hidden_dim', 128),
            num_layers=config.get('num_layers', 2),
            output_dim=1,
            dropout=config.get('dropout', 0.2)
        )
    elif model_type == 'neural_cde':
        model = NeuralCDEForecaster(
            input_channels=input_dim,
            hidden_channels=config.get('hidden_dim', 128),
            output_channels=1,
            interpolation=config.get('interpolation', 'cubic')
        )
    elif model_type == 'realdiffusionnet':
        # 确定图像嵌入维度
        image_embed_dim = config.get('image_embed_dim', 128)
        if not config.get('use_multimodal', False):
            image_embed_dim = 0
            
        model = RealDiffFusionNetForecaster(
            input_channels=input_dim,
            hidden_channels=config.get('hidden_dim', 128),
            image_embed_dim=image_embed_dim,
            output_dim=1,
            attention_dim=config.get('attention_dim', 128),
            num_heads=config.get('num_heads', 4),
            ff_dim=config.get('ff_dim', 256),
            num_layers=config.get('num_layers', 2),
            dropout=config.get('dropout', 0.1),
            fusion_type=config.get('fusion_type', 'concat'),
            interpolation=config.get('interpolation', 'rectilinear')
        )
    else:
        raise ValueError(f"Unknown model type: {model_type}")
    
    # 加载模型参数
    if os.path.exists(model_path):
        checkpoint = torch.load(model_path, map_location=torch.device('cpu'))
        model.load_state_dict(checkpoint['model_state_dict'])
        print(f"Loaded model from {model_path}")
    else:
        raise FileNotFoundError(f"Model file not found: {model_path}")
    
    return model

def evaluate_all_models(model_paths, dataset_name, data_dir, config, output_dir):
    """
    评估多个模型并比较其性能
    
    参数:
    model_paths: 模型路径字典，键为模型名称，值为模型文件路径
    dataset_name: 数据集名称
    data_dir: 数据目录路径
    config: 配置字典
    output_dir: 输出目录
    """
    # 创建数据加载器
    _, _, test_loader = create_dataloaders(
        dataset_name=dataset_name,
        data_dir=data_dir,
        batch_size=config.get('batch_size', 32),
        use_images=config.get('use_multimodal', False),
        max_seq_len=config.get('max_seq_len', None)
    )
    
    if test_loader is None:
        print("Failed to create test dataloader. Exiting.")
        return
    
    # 确定输入特征维度
    sample_batch = next(iter(test_loader))
    input_dim = sample_batch['features'].shape[2]
    
    # 评估每个模型
    results = {}
    device = torch.device('cuda' if torch.cuda.is_available() and config.get('device', 'cuda') == 'cuda' else 'cpu')
    criterion = nn.MSELoss()
    
    for model_name, model_path in model_paths.items():
        print(f"Evaluating {model_name}...")
        
        # 解析模型类型
        model_type = model_name.split('_')[0] if '_' in model_name else model_name
        
        # 加载模型
        model = load_model(model_path, model_type, input_dim, config)
        model.to(device)
        model.eval()
        
        # 在测试集上评估
        test_loss, test_metrics = evaluate_model(model, test_loader, criterion, device)
        
        # 存储结果
        results[model_name] = {
            'loss': test_loss,
            'rmse': test_metrics['rmse'],
            'mae': test_metrics['mae'],
            'r2': test_metrics['r2'],
            'y_true': test_metrics['y_true'],
            'y_pred': test_metrics['y_pred']
        }
        
        # 绘制预测值与真实值比较
        plot_predictions(
            test_metrics['y_true'],
            test_metrics['y_pred'],
            title=f"{model_name} Predictions",
            save_path=os.path.join(output_dir, f"{model_name}_predictions.png")
        )
        
        print(f"{model_name} - Test Loss: {test_loss:.4f}, Test RMSE: {test_metrics['rmse']:.4f}")
    
    # 比较所有模型的RMSE
    model_comparison = {name: {'rmse': res['rmse']} for name, res in results.items()}
    plot_model_comparison(
        model_comparison,
        metric='rmse',
        title='Model Comparison (RMSE)',
        save_path=os.path.join(output_dir, 'model_comparison_rmse.png')
    )
    
    # 比较所有模型的MAE
    model_comparison = {name: {'mae': res['mae']} for name, res in results.items()}
    plot_model_comparison(
        model_comparison,
        metric='mae',
        title='Model Comparison (MAE)',
        save_path=os.path.join(output_dir, 'model_comparison_mae.png')
    )
    
    # 比较所有模型的R²
    model_comparison = {name: {'r2': res['r2']} for name, res in results.items()}
    plot_model_comparison(
        model_comparison,
        metric='r2',
        title='Model Comparison (R²)',
        save_path=os.path.join(output_dir, 'model_comparison_r2.png')
    )
    
    # 保存结果到CSV文件
    results_df = pd.DataFrame(columns=['Model', 'Loss', 'RMSE', 'MAE', 'R²'])
    for i, (name, res) in enumerate(results.items()):
        results_df.loc[i] = [name, res['loss'], res['rmse'], res['mae'], res['r2']]
    
    results_df.to_csv(os.path.join(output_dir, 'model_comparison.csv'), index=False)
    print(f"Saved model comparison results to {os.path.join(output_dir, 'model_comparison.csv')}")
    
    return results

def visualize_disease_progression(model, patient_id, dataset_name, data_dir, config, output_dir):
    """
    可视化特定患者的疾病进展预测
    
    参数:
    model: 预训练模型
    patient_id: 患者ID
    dataset_name: 数据集名称
    data_dir: 数据目录路径
    config: 配置字典
    output_dir: 输出目录
    """
    # 创建数据加载器（这里我们只需要测试加载器）
    _, _, test_loader = create_dataloaders(
        dataset_name=dataset_name,
        data_dir=data_dir,
        batch_size=1,  # 批大小为1，以便处理单个患者
        use_images=config.get('use_multimodal', False),
        max_seq_len=None
    )
    
    if test_loader is None:
        print("Failed to create test dataloader. Exiting.")
        return
    
    # 查找指定患者的数据
    patient_data = None
    for batch in test_loader:
        if batch['patient_id'][0] == patient_id:
            patient_data = batch
            break
    
    if patient_data is None:
        print(f"Patient {patient_id} not found in test set.")
        return
    
    # 准备预测
    device = torch.device('cuda' if torch.cuda.is_available() and config.get('device', 'cuda') == 'cuda' else 'cpu')
    model.to(device)
    model.eval()
    
    with torch.no_grad():
        # 获取患者的特征序列
        features = patient_data['features'].to(device)
        
        # 如果有图像数据
        if 'image' in patient_data:
            images = patient_data['image'].to(device)
            outputs = model(features, images=images)
        else:
            outputs = model(features)
        
        # 如果输出是3D的，转换为2D
        if outputs.dim() > 2:
            outputs = outputs.squeeze(0)  # [seq_len, output_dim]
        
        # 准备可视化数据
        weeks = patient_data['features'][0, :, 5].cpu().numpy()  # 假设周数在索引5
        actual_fvc = patient_data['features'][0, :, 3].cpu().numpy()  # 假设FVC在索引3
        predicted_fvc = outputs[:, 0].cpu().numpy()
        
        # 将预测值扩展到实际值的长度
        if len(predicted_fvc) < len(actual_fvc):
            # 将剩余时间点的FVC预测为NaN
            predicted_fvc = np.concatenate([predicted_fvc, np.full(len(actual_fvc) - len(predicted_fvc), np.nan)])
        
        # 准备历史和预测数据字典
        history_data = {
            'weeks': weeks,
            'fvc': actual_fvc
        }
        
        prediction_data = {
            'weeks': weeks,
            'fvc': predicted_fvc
        }
        
        # 绘制疾病进展曲线
        plot_disease_progression(
            history_data,
            prediction_data,
            title=f"Patient {patient_id} FVC Progression",
            save_path=os.path.join(output_dir, f"patient_{patient_id}_progression.png")
        )
        
        print(f"Saved disease progression visualization for patient {patient_id}")

def visualize_attention(model, dataset_name, data_dir, config, output_dir):
    """
    可视化注意力权重
    
    参数:
    model: 具有注意力机制的预训练模型
    dataset_name: 数据集名称
    data_dir: 数据目录路径
    config: 配置字典
    output_dir: 输出目录
    """
    # 仅对RealDiffFusionNet模型可视化注意力
    if not isinstance(model, RealDiffFusionNetForecaster):
        print("Attention visualization only available for RealDiffFusionNet models.")
        return
    
    # 创建数据加载器
    _, _, test_loader = create_dataloaders(
        dataset_name=dataset_name,
        data_dir=data_dir,
        batch_size=1,  # 批大小为1，以便可视化单个样本的注意力
        use_images=config.get('use_multimodal', False),
        max_seq_len=None
    )
    
    if test_loader is None:
        print("Failed to create test dataloader. Exiting.")
        return
    
    # 获取一个样本
    sample_batch = next(iter(test_loader))
    
    # 准备数据
    device = torch.device('cuda' if torch.cuda.is_available() and config.get('device', 'cuda') == 'cuda' else 'cpu')
    features = sample_batch['features'].to(device)
    
    # 如果有图像数据
    if 'image' in sample_batch:
        images = sample_batch['image'].to(device)
    else:
        images = None
    
    # 获取注意力权重
    model.to(device)
    model.eval()
    
    # 注意：这是一个简化的假设，实际的注意力权重提取需要根据模型架构进行调整
    # 以下是一个假设的实现
    with torch.no_grad():
        # 前向传播
        if images is not None:
            _ = model(features, images=images)
        else:
            _ = model(features)
        
        # 假设模型有一个注意力层，我们可以提取其权重
        # 这需要根据实际模型结构进行调整
        if hasattr(model, 'model') and hasattr(model.model, 'attention_layers'):
            # 提取第一个注意力层的权重
            attention_layer = model.model.attention_layers[0]
            if hasattr(attention_layer, 'attention'):
                if hasattr(attention_layer.attention, 'last_attn_weights'):
                    attn_weights = attention_layer.attention.last_attn_weights
                    
                    # 可视化
                    seq_len = features.size(1)
                    tokens = [f"t{i}" for i in range(seq_len)]
                    
                    plot_attention_weights(
                        attn_weights[0].cpu().numpy(),  # 取第一个批次样本
                        tokens=tokens,
                        title="Attention Weights",
                        save_path=os.path.join(output_dir, "attention_weights.png")
                    )
                    
                    print("Saved attention weights visualization")
                else:
                    print("Model does not have last_attn_weights attribute.")
            else:
                print("Attention layer does not have attention attribute.")
        else:
            print("Model does not have accessible attention layers.")

def export_model(model, save_path, input_shape=None, image_shape=None):
    """
    导出模型为ONNX格式
    
    参数:
    model: 要导出的模型
    save_path: 导出路径，应以.onnx结尾
    input_shape: 输入张量形状，默认为(1, 10, 6)，表示(batch_size, seq_len, input_channels)
    image_shape: 图像输入形状，默认为(1, 3, 224, 224)，如果为None则不包括图像输入
    
    返回:
    是否导出成功
    """
    try:
        import torch
        import os
        
        # 设置为评估模式
        model.eval()
        
        # 确保目录存在
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        
        # 设置默认输入形状
        if input_shape is None:
            input_shape = (1, 10, 6)  # (batch_size, seq_len, input_channels)
        
        # 创建示例输入
        dummy_input = torch.randn(*input_shape, device=next(model.parameters()).device)
        
        # 如果有图像输入
        if image_shape is not None:
            dummy_image = torch.randn(*image_shape, device=next(model.parameters()).device)
            
            # 导出包含图像输入的模型
            torch.onnx.export(
                model, 
                (dummy_input, dummy_image),  # 模型输入
                save_path,  # 保存路径
                export_params=True,  # 存储训练好的参数权重
                opset_version=12,  # ONNX算子集版本
                do_constant_folding=True,  # 是否执行常量折叠优化
                input_names=['input', 'image'],  # 输入名称
                output_names=['output'],  # 输出名称
                dynamic_axes={  # 动态维度
                    'input': {0: 'batch_size', 1: 'sequence_length'},
                    'image': {0: 'batch_size'},
                    'output': {0: 'batch_size', 1: 'sequence_length'}
                }
            )
        else:
            # 导出不包含图像输入的模型
            torch.onnx.export(
                model, 
                dummy_input,  # 模型输入
                save_path,  # 保存路径
                export_params=True,  # 存储训练好的参数权重
                opset_version=12,  # ONNX算子集版本
                do_constant_folding=True,  # 是否执行常量折叠优化
                input_names=['input'],  # 输入名称
                output_names=['output'],  # 输出名称
                dynamic_axes={  # 动态维度
                    'input': {0: 'batch_size', 1: 'sequence_length'},
                    'output': {0: 'batch_size', 1: 'sequence_length'}
                }
            )
        
        print(f"模型已成功导出至: {save_path}")
        
        # 验证导出的模型（可选）
        try:
            import onnx
            onnx_model = onnx.load(save_path)
            onnx.checker.check_model(onnx_model)
            print("ONNX模型格式验证通过")
        except ImportError:
            print("未安装onnx库，跳过模型验证")
        except Exception as e:
            print(f"ONNX模型验证失败: {e}")
            
        return True
    
    except Exception as e:
        print(f"模型导出失败: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='评估RealDiffFusionNet模型')
    parser.add_argument('--model_path', type=str, required=True,
                        help='模型文件路径')
    parser.add_argument('--model_type', type=str, default='realdiffusionnet',
                        help='模型类型（lstm, neural_cde, realdiffusionnet）')
    parser.add_argument('--dataset', type=str, default='osic',
                        help='数据集名称（osic或adni）')
    parser.add_argument('--data_path', type=str, required=True,
                        help='数据目录路径')
    parser.add_argument('--config', type=str, default=None,
                        help='配置文件路径')
    parser.add_argument('--output_dir', type=str, default='evaluation_results',
                        help='输出目录')
    parser.add_argument('--compare_models', action='store_true',
                        help='比较多个模型的性能')
    parser.add_argument('--model_paths', type=str, nargs='+', default=[],
                        help='要比较的多个模型的路径，格式为name:path')
    parser.add_argument('--patient_id', type=str, default=None,
                        help='要可视化的患者ID')
    parser.add_argument('--visualize_attention', action='store_true',
                        help='可视化注意力权重')
    parser.add_argument('--task', type=str, default='regression',
                        help='任务类型：regression或classification')
    
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 加载配置
    config = {}
    if args.config and os.path.exists(args.config):
        with open(args.config, 'r') as f:
            config = yaml.safe_load(f)
        print(f"Loaded configuration from {args.config}")
    
    # 更新配置
    config.update(vars(args))
    
    # 创建数据加载器以获取输入维度
    _, _, test_loader = create_dataloaders(
        dataset_name=args.dataset,
        data_dir=args.data_path,
        batch_size=config.get('batch_size', 32),
        use_images=config.get('use_multimodal', False),
        max_seq_len=config.get('max_seq_len', None),
        task=config.get('task', 'regression')  # 传递任务类型
    )
    
    if test_loader is None:
        print("Failed to create test dataloader. Exiting.")
        return
    
    # 确定输入特征维度
    sample_batch = next(iter(test_loader))
    input_dim = sample_batch['features'].shape[2]
    
    # 比较多个模型
    if args.compare_models:
        if not args.model_paths:
            print("Please provide model paths to compare.")
            return
        
        # 解析模型路径
        model_paths = {}
        for path_str in args.model_paths:
            if ':' in path_str:
                name, path = path_str.split(':', 1)
                model_paths[name] = path
            else:
                name = os.path.basename(path_str).split('.')[0]
                model_paths[name] = path_str
        
        # 评估所有模型
        results = evaluate_all_models(
            model_paths,
            args.dataset,
            args.data_path,
            config,
            args.output_dir
        )
    else:
        # 加载单个模型
        model = load_model(args.model_path, args.model_type, input_dim, config)
        device = torch.device('cuda' if torch.cuda.is_available() and config.get('device', 'cuda') == 'cuda' else 'cpu')
        model.to(device)
        
        # 根据任务类型选择损失函数
        if args.task == 'classification':
            criterion = nn.CrossEntropyLoss()
        else:  # regression
            criterion = nn.MSELoss()
        
        # 评估模型
        test_loss, test_metrics = evaluate_model(
            model, test_loader, criterion, device, 
            task=args.task  # 传递任务类型
        )
        
        # 输出评估结果
        print(f"Test Loss: {test_loss:.4f}")
        if args.task == 'classification':
            print(f"Test Accuracy: {test_metrics['accuracy']:.4f}, "
                  f"Test F1: {test_metrics['f1']:.4f}, "
                  f"Test Precision: {test_metrics['precision']:.4f}, "
                  f"Test Recall: {test_metrics['recall']:.4f}")
            
            # 获取详细评估指标
            _, all_metrics, y_true, y_pred, _ = evaluate_model(
                model, test_loader, criterion, device,
                task=args.task,
                compute_metrics=True,
                return_predictions=True
            )
            
            # 绘制混淆矩阵
            if config.get('visualization', {}).get('plot_confusion_matrix', True):
                # 获取类别标签
                if args.dataset == 'adni':
                    classes = ['CN', 'MCI', 'AD']  # ADNI数据集的类别名称
                else:
                    n_classes = len(np.unique(np.concatenate([y_true, y_pred])))
                    classes = [str(i) for i in range(n_classes)]
                
                # 绘制并保存混淆矩阵
                from utils.visualization import plot_confusion_matrix
                cm = plot_confusion_matrix(
                    y_true, y_pred,
                    classes=classes,
                    title=f"{args.model_type} {args.dataset} 混淆矩阵",
                    save_path=os.path.join(args.output_dir, f"{args.model_type}_{args.dataset}_confusion_matrix.png"),
                    show_plot=False
                )
                print("已保存混淆矩阵")
                
                # 绘制归一化混淆矩阵
                plot_confusion_matrix(
                    y_true, y_pred,
                    classes=classes,
                    normalize=True,
                    title=f"{args.model_type} {args.dataset} 归一化混淆矩阵",
                    save_path=os.path.join(args.output_dir, f"{args.model_type}_{args.dataset}_confusion_matrix_norm.png"),
                    show_plot=False
                )
                print("已保存归一化混淆矩阵")
        else:  # 回归任务
            print(f"Test RMSE: {test_metrics['rmse']:.4f}, "
                  f"Test MAE: {test_metrics['mae']:.4f}, "
                  f"Test R²: {test_metrics['r2']:.4f}")
        
        # 绘制预测结果
        plot_predictions(
            test_metrics['y_true'],
            test_metrics['y_pred'],
            title=f"{args.model_type.upper()} Predictions",
            save_path=os.path.join(args.output_dir, f"{args.model_type}_predictions.png")
        )
        
        # 如果指定了患者ID，可视化疾病进展
        if args.patient_id:
            visualize_disease_progression(
                model,
                args.patient_id,
                args.dataset,
                args.data_path,
                config,
                args.output_dir
            )
        
        # 可视化注意力权重
        if args.visualize_attention and args.model_type == 'realdiffusionnet':
            visualize_attention(
                model,
                args.dataset,
                args.data_path,
                config,
                args.output_dir
            )

if __name__ == "__main__":
    main() 