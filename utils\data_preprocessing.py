import os
import numpy as np
import pandas as pd
import torch
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
import cv2
from PIL import Image
import pydicom
from typing import Dict, List, Tuple, Optional
import torch.nn.functional as F
from torch.nn.utils.rnn import pad_sequence

class OSICDataPreprocessor:
    """
    OSIC肺纤维化数据集预处理器
    """
    def __init__(self, data_dir):
        """
        初始化
        
        参数:
        data_dir: 数据目录路径
        """
        self.data_dir = data_dir
        self.train_df = None
        self.test_df = None
        self.scaler = StandardScaler()
        self.label_encoders = {}
    
    def load_data(self):
        """
        加载OSIC数据集
        """
        # 加载训练数据
        train_path = os.path.join(self.data_dir, 'train.csv')
        train_df = pd.read_csv(train_path)
        
        # 加载测试数据
        test_path = os.path.join(self.data_dir, 'test.csv')
        test_df = pd.read_csv(test_path)
        
        self.train_df = train_df
        self.test_df = test_df
        
        return train_df, test_df
    
    def preprocess_structured_data(self):
        """
        预处理结构化数据
        """
        # 确保数据已加载
        if self.train_df is None:
            self.load_data()
        
        # 处理分类特征前清理文本数据（移除尾随空格）
        for col in self.train_df.select_dtypes(include=['object']).columns:
            if col != 'Patient':  # 不处理患者ID
                self.train_df[col] = self.train_df[col].str.strip()
                if self.test_df is not None:
                    self.test_df[col] = self.test_df[col].str.strip()
        
        # 处理分类特征
        categorical_features = ['Sex', 'SmokingStatus']
        for feature in categorical_features:
            le = LabelEncoder()
            self.train_df[feature] = le.fit_transform(self.train_df[feature])
            self.test_df[feature] = le.transform(self.test_df[feature])
            self.label_encoders[feature] = le
        
        # 标准化连续特征
        continuous_features = ['Age', 'FVC', 'Percent', 'Weeks']
        self.scaler.fit(self.train_df[continuous_features])
        
        self.train_df[continuous_features] = self.scaler.transform(self.train_df[continuous_features])
        self.test_df[continuous_features] = self.scaler.transform(self.test_df[continuous_features])
        
        return self.train_df, self.test_df
    
    def load_ct_scan(self, patient_id):
        """
        加载特定患者的CT扫描
        
        参数:
        patient_id: 患者ID
        
        返回:
        CT扫描切片列表
        """
        # CT扫描目录路径
        ct_dir = os.path.join(self.data_dir, 'train', patient_id)
        
        # 检查目录是否存在
        if not os.path.exists(ct_dir):
            return None
        
        # 读取所有DICOM文件
        slices = []
        for file in os.listdir(ct_dir):
            if file.endswith('.dcm'):
                file_path = os.path.join(ct_dir, file)
                try:
                    dcm = pydicom.dcmread(file_path)
                    img = dcm.pixel_array
                    
                    # 归一化像素值
                    img = (img - np.min(img)) / (np.max(img) - np.min(img) + 1e-8)
                    
                    # 调整大小到224x224
                    img = cv2.resize(img, (224, 224))
                    
                    # 添加到切片列表
                    slices.append(img)
                except Exception as e:
                    print(f"Error loading DICOM file {file_path}: {e}")
        
        # 转换为NumPy数组
        if slices:
            slices = np.array(slices)
        
        return slices
    
    def prepare_time_series_data(self):
        """
        准备时间序列数据，按患者分组
        
        返回:
        按患者分组的时间序列数据
        """
        # 确保数据已预处理
        if self.train_df is None or 'SmokingStatus' not in self.train_df.columns:
            self.preprocess_structured_data()
        
        # 按患者ID分组
        grouped_data = {}
        for patient_id, group in self.train_df.groupby('Patient'):
            # 按周排序
            group = group.sort_values('Weeks')
            
            # 获取特征和目标
            features = group[['Age', 'Sex', 'SmokingStatus', 'FVC', 'Percent', 'Weeks']].values
            
            # 获取CT扫描（如果可用）
            ct_slices = self.load_ct_scan(patient_id)
            
            grouped_data[patient_id] = {
                'features': features,
                'ct_slices': ct_slices
            }
        
        return grouped_data
    
    def split_data(self, test_size=0.2, val_size=0.2, random_state=42):
        """
        分割数据为训练集、验证集和测试集
        
        参数:
        test_size: 测试集比例
        val_size: 验证集比例（从训练集中分割）
        random_state: 随机种子
        
        返回:
        训练集、验证集和测试集患者ID
        """
        # 准备时间序列数据
        grouped_data = self.prepare_time_series_data()
        
        # 获取患者ID列表
        patient_ids = list(grouped_data.keys())
        
        # 分割为训练集和测试集
        train_ids, test_ids = train_test_split(
            patient_ids, 
            test_size=test_size, 
            random_state=random_state
        )
        
        # 从训练集中分割验证集
        train_ids, val_ids = train_test_split(
            train_ids, 
            test_size=val_size/(1-test_size), 
            random_state=random_state
        )
        
        return train_ids, val_ids, test_ids

class OSICDataset(Dataset):
    """
    OSIC肺纤维化数据集
    """
    def __init__(self, grouped_data, patient_ids, use_images=True, max_seq_len=None):
        """
        初始化
        
        参数:
        grouped_data: 按患者分组的数据（由OSICDataPreprocessor.prepare_time_series_data生成）
        patient_ids: 要包含的患者ID列表
        use_images: 是否使用图像数据
        max_seq_len: 最大序列长度，如果为None则不限制
        """
        self.grouped_data = grouped_data
        self.patient_ids = patient_ids
        self.use_images = use_images
        self.max_seq_len = max_seq_len
        
        # 过滤只包含指定患者的数据
        self.data = {pid: self.grouped_data[pid] for pid in patient_ids if pid in self.grouped_data}
        
        # 创建索引映射
        self.index_map = []
        for pid in self.data.keys():
            features = self.data[pid]['features']
            seq_len = len(features)
            
            # 如果指定了最大序列长度，则裁剪
            if max_seq_len is not None and seq_len > max_seq_len:
                seq_len = max_seq_len
            
            # 为每个时间步创建一个样本
            for i in range(seq_len - 1):  # -1 是因为我们需要一个下一个值作为目标
                self.index_map.append((pid, i))
    
    def __len__(self):
        """返回数据集的大小"""
        return len(self.index_map)
    
    def __getitem__(self, idx):
        """
        获取指定索引的样本
        
        参数:
        idx: 样本索引
        
        返回:
        包含特征、目标和图像的字典
        """
        pid, time_idx = self.index_map[idx]
        
        # 获取特征
        features = self.data[pid]['features']
        seq_len = len(features)
        
        # 如果指定了最大序列长度，则裁剪
        if self.max_seq_len is not None and seq_len > self.max_seq_len:
            features = features[:self.max_seq_len]
            seq_len = self.max_seq_len
        
        # 获取截止到当前时间点的特征序列
        current_seq = features[:time_idx + 1]
        
        # 下一个时间点的FVC作为目标
        next_fvc = features[time_idx + 1][3]  # FVC在索引3
        
        # 转换为张量
        current_seq_tensor = torch.tensor(current_seq, dtype=torch.float32)
        next_fvc_tensor = torch.tensor(next_fvc, dtype=torch.float32)
        
        # 准备返回字典
        sample = {
            'features': current_seq_tensor,
            'target': next_fvc_tensor,
            'patient_id': pid
        }
        
        # 如果使用图像，则添加CT切片
        if self.use_images and self.data[pid]['ct_slices'] is not None:
            ct_slices = self.data[pid]['ct_slices']
            
            # 随机选择一个切片
            if len(ct_slices) > 0:
                rand_idx = np.random.randint(0, len(ct_slices))
                ct_slice = ct_slices[rand_idx]
                
                # 转换为3通道并调整为PyTorch期望的格式
                ct_slice = np.stack([ct_slice] * 3, axis=0)  # [3, 224, 224]
                ct_slice_tensor = torch.tensor(ct_slice, dtype=torch.float32)
                
                sample['image'] = ct_slice_tensor
            else:
                # 如果没有可用的切片，使用零张量
                sample['image'] = torch.zeros(3, 224, 224, dtype=torch.float32)
        
        return sample

class ADNIDataPreprocessor:
    """
    ADNI阿尔茨海默病数据集预处理器
    """
    def __init__(self, data_dir):
        """
        初始化
        
        参数:
        data_dir: 数据目录路径
        """
        self.data_dir = data_dir
        self.data_df = None
        self.scaler = StandardScaler()
        self.label_encoders = {}
    
    def load_data(self):
        """
        加载ADNI数据集
        """
        # 加载数据
        data_path = os.path.join(self.data_dir, 'ADNIMERGE.csv')
        self.data_df = pd.read_csv(data_path)
        
        return self.data_df
    
    def preprocess_structured_data(self):
        """
        预处理结构化数据
        """
        # 确保数据已加载
        if self.data_df is None:
            self.load_data()
        
        # 处理分类特征前清理文本数据（移除尾随空格）
        for col in self.data_df.select_dtypes(include=['object']).columns:
            if col not in ['PTID', 'VISCODE']:  # 不处理患者ID和访问代码
                self.data_df[col] = self.data_df[col].str.strip()
        
        # 处理分类特征
        categorical_features = ['PTGENDER', 'PTETHCAT', 'PTRACCAT', 'PTMARRY', 'DX', 'APOE4']
        for feature in categorical_features:
            if feature in self.data_df.columns:
                le = LabelEncoder()
                self.data_df[feature] = le.fit_transform(self.data_df[feature].fillna('Unknown'))
                self.label_encoders[feature] = le
        
        # 标准化连续特征
        continuous_features = ['AGE', 'PTEDUCAT', 'CDRSB', 'ADAS13', 'MMSE', 
                              'RAVLT_immediate', 'RAVLT_learning', 'RAVLT_forgetting',
                              'FAQ', 'Ventricles', 'Hippocampus', 'WholeBrain', 'Entorhinal']
        continuous_features = [f for f in continuous_features if f in self.data_df.columns]
        
        # 填充缺失值
        for feature in continuous_features:
            self.data_df[feature] = self.data_df[feature].fillna(self.data_df[feature].mean())
        
        # 标准化
        self.scaler.fit(self.data_df[continuous_features])
        self.data_df[continuous_features] = self.scaler.transform(self.data_df[continuous_features])
        
        return self.data_df
    
    def load_mri_scan(self, patient_id, visit_code):
        """
        加载特定患者的MRI扫描
        
        参数:
        patient_id: 患者ID
        visit_code: 访问代码（如'bl'表示基线）
        
        返回:
        MRI扫描切片列表
        """
        # MRI扫描目录路径
        mri_dir = os.path.join(self.data_dir, 'MRI', patient_id, visit_code)
        
        # 检查目录是否存在
        if not os.path.exists(mri_dir):
            return None
        
        # 读取MRI文件
        slices = []
        try:
            # 使用nibabel库加载NIfTI文件
            import nibabel as nib
            
            # 查找所有的NIfTI文件
            nifti_files = [f for f in os.listdir(mri_dir) 
                          if f.endswith('.nii') or f.endswith('.nii.gz')]
            
            if not nifti_files:
                print(f"警告：在 {mri_dir} 中未找到NIfTI文件")
                return None
            
            # 加载第一个可用的NIfTI文件
            file_path = os.path.join(mri_dir, nifti_files[0])
            img = nib.load(file_path)
            data = img.get_fdata()
            
            # 获取MRI的维度
            z_dim = data.shape[2]
            
            # 选择切片策略，根据配置（从中心切片选择）
            slice_strategy = "center_three"  # 也可以从配置中读取
            
            if slice_strategy == "center_three":
                # 提取中心区域的三个切片
                middle_slice_idx = z_dim // 2
                slices_to_extract = [
                    middle_slice_idx - 2, 
                    middle_slice_idx, 
                    middle_slice_idx + 2
                ]
            elif slice_strategy == "uniform_sample":
                # 均匀采样
                num_slices = min(5, z_dim)
                step = max(1, z_dim // num_slices)
                slices_to_extract = list(range(0, z_dim, step))[:num_slices]
            else:
                # 默认：只取中心切片
                slices_to_extract = [z_dim // 2]
            
            # 提取并处理切片
            for slice_idx in slices_to_extract:
                if 0 <= slice_idx < z_dim:
                    # 获取单个切片
                    slice_data = data[:, :, slice_idx]
                    
                    # 归一化：使用minmax归一化，减少异常值影响
                    # 先剪裁极端值（可选）
                    p_low, p_high = np.percentile(slice_data, [1, 99])
                    slice_data = np.clip(slice_data, p_low, p_high)
                    
                    # 归一化到[0,1]
                    slice_data = (slice_data - np.min(slice_data)) / (np.max(slice_data) - np.min(slice_data) + 1e-8)
                    
                    # 调整大小到224x224（适配预训练CNN）
                    slice_data = cv2.resize(slice_data, (224, 224))
                    
                    # 添加到切片列表
                    slices.append(slice_data)
            
        except ImportError:
            print("警告：需要安装nibabel库以处理NIfTI文件")
            return None
        except Exception as e:
            print(f"加载MRI文件时出错: {e}")
            return None
        
        # 如果没有成功加载切片，返回None
        if len(slices) == 0:
            return None
            
        # 转换为NumPy数组
        slices = np.array(slices)
        
        return slices
    
    def prepare_time_series_data(self):
        """
        准备时间序列数据，按患者分组
        
        返回:
        按患者分组的时间序列数据
        """
        # 确保数据已预处理
        if self.data_df is None:
            self.preprocess_structured_data()
        
        # 按患者ID分组
        grouped_data = {}
        for (rid, _), group in self.data_df.groupby(['RID', 'VISCODE']):
            # 将RID转换为字符串作为键
            patient_id = str(rid)
            
            if patient_id not in grouped_data:
                grouped_data[patient_id] = {
                    'features': [],
                    'mri_slices': [],
                    'diagnoses': [], # 诊断结果（用于分类任务）
                    'cognitive_scores': [] # 认知评分（用于回归任务）
                }
            
            # 提取特征
            if 'DX' in group.columns and 'MMSE' in group.columns:
                # 包含诊断和认知评分的核心特征
                features = group[['AGE', 'PTGENDER', 'PTEDUCAT', 'CDRSB', 'ADAS13', 'MMSE', 'Month']].values[0]
                diagnosis = group['DX'].values[0]  # 0:CN, 1:MCI, 2:AD
                cognitive_score = group['MMSE'].values[0]
                
                # 保存特征和标签
                grouped_data[patient_id]['features'].append(features)
                grouped_data[patient_id]['diagnoses'].append(diagnosis)
                grouped_data[patient_id]['cognitive_scores'].append(cognitive_score)
                
                # 获取MRI扫描
                visit_code = group['VISCODE'].values[0]
                mri_slices = self.load_mri_scan(patient_id, visit_code)
                if mri_slices is not None:
                    grouped_data[patient_id]['mri_slices'].append(mri_slices)
        
        # 对特征按月份排序并转换为numpy数组
        for patient_id in grouped_data:
            if grouped_data[patient_id]['features']:
                # 提取特征、诊断和认知评分
                features = grouped_data[patient_id]['features']
                diagnoses = grouped_data[patient_id]['diagnoses']
                cognitive_scores = grouped_data[patient_id]['cognitive_scores']
                
                # 按月份排序（Month在索引6）
                sorted_indices = sorted(range(len(features)), key=lambda i: features[i][6])
                
                # 按排序索引重新组织数据
                grouped_data[patient_id]['features'] = np.array([features[i] for i in sorted_indices])
                grouped_data[patient_id]['diagnoses'] = np.array([diagnoses[i] for i in sorted_indices])
                grouped_data[patient_id]['cognitive_scores'] = np.array([cognitive_scores[i] for i in sorted_indices])
                
                # 如果有MRI切片，也相应排序
                if grouped_data[patient_id]['mri_slices']:
                    grouped_data[patient_id]['mri_slices'] = [grouped_data[patient_id]['mri_slices'][i] for i in sorted_indices if i < len(grouped_data[patient_id]['mri_slices'])]
        
        return grouped_data
    
    def split_data(self, test_size=0.2, val_size=0.2, random_state=42):
        """
        分割数据为训练集、验证集和测试集
        
        参数:
        test_size: 测试集比例
        val_size: 验证集比例（从训练集中分割）
        random_state: 随机种子
        
        返回:
        训练集、验证集和测试集患者ID
        """
        # 准备时间序列数据
        grouped_data = self.prepare_time_series_data()
        
        # 获取患者ID列表
        patient_ids = list(grouped_data.keys())
        
        # 分割为训练集和测试集
        train_ids, test_ids = train_test_split(
            patient_ids, 
            test_size=test_size, 
            random_state=random_state
        )
        
        # 从训练集中分割验证集
        train_ids, val_ids = train_test_split(
            train_ids, 
            test_size=val_size/(1-test_size), 
            random_state=random_state
        )
        
        return train_ids, val_ids, test_ids

class ADNIDataset(Dataset):
    """
    ADNI阿尔茨海默病数据集
    """
    def __init__(self, grouped_data, patient_ids, task='regression', use_images=True, max_seq_len=None):
        """
        初始化
        
        参数:
        grouped_data: 按患者分组的数据（由ADNIDataPreprocessor.prepare_time_series_data生成）
        patient_ids: 要包含的患者ID列表
        task: 任务类型，'regression'（认知评分预测）或'classification'（疾病分类）
        use_images: 是否使用图像数据
        max_seq_len: 最大序列长度，如果为None则不限制
        """
        self.grouped_data = grouped_data
        self.patient_ids = patient_ids
        self.task = task
        self.use_images = use_images
        self.max_seq_len = max_seq_len
        
        # 过滤只包含指定患者的数据
        self.data = {pid: self.grouped_data[pid] for pid in patient_ids if pid in self.grouped_data}
        
        # 创建索引映射
        self.index_map = []
        for pid in self.data.keys():
            if 'features' in self.data[pid] and len(self.data[pid]['features']) > 0:
                features = self.data[pid]['features']
                seq_len = len(features)
                
                # 如果指定了最大序列长度，则裁剪
                if max_seq_len is not None and seq_len > max_seq_len:
                    seq_len = max_seq_len
                
                # 为每个时间步创建一个样本，除了最后一个（需要有下一个时间步作为目标）
                for i in range(seq_len - 1):
                    self.index_map.append((pid, i))
    
    def __len__(self):
        """返回数据集的大小"""
        return len(self.index_map)
    
    def __getitem__(self, idx):
        """
        获取指定索引的样本
        
        参数:
        idx: 样本索引
        
        返回:
        包含特征、目标和图像的字典
        """
        pid, time_idx = self.index_map[idx]
        
        # 获取特征
        features = self.data[pid]['features']
        seq_len = len(features)
        
        # 如果指定了最大序列长度，则裁剪
        if self.max_seq_len is not None and seq_len > self.max_seq_len:
            features = features[:self.max_seq_len]
            seq_len = self.max_seq_len
        
        # 获取截止到当前时间点的特征序列
        current_seq = features[:time_idx + 1]
        
        # 准备目标值
        if self.task == 'regression':
            # 下一个时间点的认知评分作为目标
            target = self.data[pid]['cognitive_scores'][time_idx + 1]
        else:  # classification
            # 下一个时间点的诊断结果作为目标
            target = self.data[pid]['diagnoses'][time_idx + 1]
        
        # 转换为张量
        current_seq_tensor = torch.tensor(current_seq, dtype=torch.float32)
        target_tensor = torch.tensor(target, dtype=torch.float32 if self.task == 'regression' else torch.long)
        
        # 准备返回字典
        sample = {
            'features': current_seq_tensor,
            'target': target_tensor,
            'patient_id': pid
        }
        
        # 如果使用图像，则添加MRI切片
        if self.use_images and 'mri_slices' in self.data[pid] and len(self.data[pid]['mri_slices']) > time_idx:
            mri_slices = self.data[pid]['mri_slices'][time_idx]
            
            if len(mri_slices) > 0:
                # 随机选择一个切片
                rand_idx = np.random.randint(0, len(mri_slices))
                mri_slice = mri_slices[rand_idx]
                
                # 转换为3通道并调整为PyTorch期望的格式
                mri_slice = np.stack([mri_slice] * 3, axis=0)  # [3, 224, 224]
                mri_slice_tensor = torch.tensor(mri_slice, dtype=torch.float32)
                
                sample['image'] = mri_slice_tensor
            else:
                # 如果没有可用的切片，使用零张量
                sample['image'] = torch.zeros(3, 224, 224, dtype=torch.float32)
        else:
            # 如果没有可用的MRI切片，使用零张量
            sample['image'] = torch.zeros(3, 224, 224, dtype=torch.float32)
        
        return sample

def save_preprocessed_data(grouped_data, cache_path, dataset_name):
    """
    将预处理后的数据缓存为HDF5格式
    
    参数:
    grouped_data: 预处理后的分组数据
    cache_path: 缓存文件路径
    dataset_name: 数据集名称，用于缓存文件命名
    
    返回:
    是否成功保存
    """
    try:
        import h5py
        import os
        
        # 确保目录存在
        os.makedirs(os.path.dirname(cache_path), exist_ok=True)
        
        # 创建HDF5文件
        with h5py.File(cache_path, 'w') as f:
            # 保存元数据
            f.attrs['dataset_name'] = dataset_name
            f.attrs['creation_time'] = np.string_(pd.Timestamp.now().isoformat())
            f.attrs['num_patients'] = len(grouped_data)
            
            # 为每个患者创建一个组
            for patient_id, patient_data in grouped_data.items():
                patient_group = f.create_group(f"patient_{patient_id}")
                
                # 保存结构化特征
                if 'features' in patient_data and len(patient_data['features']) > 0:
                    features = np.array(patient_data['features'])
                    patient_group.create_dataset('features', data=features, compression='gzip')
                
                # 如果有图像数据
                if 'ct_slices' in patient_data and patient_data['ct_slices'] is not None:
                    ct_slices = np.array(patient_data['ct_slices'])
                    patient_group.create_dataset('ct_slices', data=ct_slices, compression='gzip')
                
                # 如果有MRI切片
                if 'mri_slices' in patient_data and patient_data['mri_slices'] is not None:
                    # 创建MRI切片组
                    mri_group = patient_group.create_group('mri_slices')
                    
                    # 遍历每个时间点的MRI切片
                    for i, slices in enumerate(patient_data['mri_slices']):
                        if slices is not None and len(slices) > 0:
                            mri_group.create_dataset(f'timepoint_{i}', data=slices, compression='gzip')
                
                # ADNI特有数据
                if 'diagnoses' in patient_data:
                    diagnoses = np.array(patient_data['diagnoses'])
                    patient_group.create_dataset('diagnoses', data=diagnoses)
                
                if 'cognitive_scores' in patient_data:
                    cognitive_scores = np.array(patient_data['cognitive_scores'])
                    patient_group.create_dataset('cognitive_scores', data=cognitive_scores)
        
        print(f"成功将预处理数据缓存到: {cache_path}")
        return True
    
    except Exception as e:
        print(f"保存预处理数据时出错: {e}")
        return False

def load_preprocessed_data(cache_path):
    """
    从HDF5缓存文件加载预处理数据
    
    参数:
    cache_path: 缓存文件路径
    
    返回:
    加载的分组数据，如果加载失败则返回None
    """
    try:
        import h5py
        import os
        
        # 检查文件是否存在
        if not os.path.exists(cache_path):
            print(f"缓存文件不存在: {cache_path}")
            return None
        
        # 加载数据
        grouped_data = {}
        
        with h5py.File(cache_path, 'r') as f:
            # 读取元数据
            dataset_name = f.attrs['dataset_name']
            creation_time = f.attrs['creation_time']
            num_patients = f.attrs['num_patients']
            
            print(f"加载缓存数据: {dataset_name}, 创建时间: {creation_time}, 患者数: {num_patients}")
            
            # 遍历所有患者
            for patient_key in f.keys():
                patient_id = patient_key.replace('patient_', '')
                patient_group = f[patient_key]
                
                # 初始化患者数据字典
                grouped_data[patient_id] = {}
                
                # 加载结构化特征
                if 'features' in patient_group:
                    grouped_data[patient_id]['features'] = patient_group['features'][()]
                
                # 加载CT切片
                if 'ct_slices' in patient_group:
                    grouped_data[patient_id]['ct_slices'] = patient_group['ct_slices'][()]
                
                # 加载MRI切片
                if 'mri_slices' in patient_group:
                    mri_group = patient_group['mri_slices']
                    mri_slices = []
                    
                    # 按顺序加载所有时间点的切片
                    for i in range(len(mri_group)):
                        timepoint_key = f'timepoint_{i}'
                        if timepoint_key in mri_group:
                            mri_slices.append(mri_group[timepoint_key][()])
                    
                    grouped_data[patient_id]['mri_slices'] = mri_slices
                
                # ADNI特有数据
                if 'diagnoses' in patient_group:
                    grouped_data[patient_id]['diagnoses'] = patient_group['diagnoses'][()]
                
                if 'cognitive_scores' in patient_group:
                    grouped_data[patient_id]['cognitive_scores'] = patient_group['cognitive_scores'][()]
        
        print(f"成功加载 {len(grouped_data)} 位患者的预处理数据")
        return grouped_data
    
    except Exception as e:
        print(f"加载预处理数据时出错: {e}")
        return None

def variable_length_collate_fn(batch):
    """
    自定义的collate_fn函数，用于处理不同长度的序列
    
    参数:
    batch: 批次数据
    
    返回:
    批处理后的数据
    """
    # 提取所有的键
    keys = batch[0].keys()
    
    result = {}
    
    for key in keys:
        if key == 'features':
            # 对特征使用pad_sequence进行填充
            features = [item[key] for item in batch]
            # 记录原始长度
            lengths = [len(f) for f in features]
            # 填充序列
            padded_features = pad_sequence(features, batch_first=True, padding_value=0.0)
            result[key] = padded_features
            # 添加长度信息，用于mask或attention机制
            result['lengths'] = torch.tensor(lengths, dtype=torch.long)
        elif key == 'patient_id':
            # 将患者ID收集为列表
            result[key] = [item[key] for item in batch]
        else:
            # 对其他键使用标准的堆叠操作
            try:
                result[key] = torch.stack([item[key] for item in batch])
            except:
                # 如果不能堆叠，则保留为列表
                result[key] = [item[key] for item in batch]
    
    return result

def create_dataloaders(dataset_name, data_dir, batch_size=32, use_images=True, max_seq_len=None, task='regression'):
    """
    创建数据加载器
    
    参数:
    dataset_name: 数据集名称（'osic'或'adni'）
    data_dir: 数据目录路径
    batch_size: 批大小
    use_images: 是否使用图像数据
    max_seq_len: 最大序列长度
    task: 对于ADNI数据集，任务类型（'regression'或'classification'）
    
    返回:
    训练、验证和测试数据加载器
    """
    if dataset_name.lower() == 'osic':
        # 准备OSIC数据
        preprocessor = OSICDataPreprocessor(data_dir)
        grouped_data = preprocessor.prepare_time_series_data()
        train_ids, val_ids, test_ids = preprocessor.split_data()
        
        # 创建数据集
        train_dataset = OSICDataset(grouped_data, train_ids, use_images, max_seq_len)
        val_dataset = OSICDataset(grouped_data, val_ids, use_images, max_seq_len)
        test_dataset = OSICDataset(grouped_data, test_ids, use_images, max_seq_len)
    
    elif dataset_name.lower() == 'adni':
        # 处理ADNI数据集
        preprocessor = ADNIDataPreprocessor(data_dir)
        grouped_data = preprocessor.prepare_time_series_data()
        train_ids, val_ids, test_ids = preprocessor.split_data()
        
        # 创建数据集
        train_dataset = ADNIDataset(grouped_data, train_ids, task, use_images, max_seq_len)
        val_dataset = ADNIDataset(grouped_data, val_ids, task, use_images, max_seq_len)
        test_dataset = ADNIDataset(grouped_data, test_ids, task, use_images, max_seq_len)
    
    else:
        raise ValueError(f"未知数据集: {dataset_name}")
    
    # 创建数据加载器，使用自定义的collate_fn
    if train_dataset is not None:
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, 
                                 collate_fn=variable_length_collate_fn)
        val_loader = DataLoader(val_dataset, batch_size=batch_size,
                               collate_fn=variable_length_collate_fn)
        test_loader = DataLoader(test_dataset, batch_size=batch_size,
                                collate_fn=variable_length_collate_fn)
        
        return train_loader, val_loader, test_loader
    
    return None, None, None

def validate_data_integrity(data_df):
    """
    验证数据完整性，检查关键字段的缺失值比例
    
    参数:
    data_df: 数据DataFrame
    
    返回:
    是否通过验证，以及详细的验证结果
    """
    # 计算每列的缺失值比例
    missing_ratio = data_df.isnull().sum() / len(data_df)
    
    # 检查是否有列的缺失值超过50%
    critical_columns = []
    warning_columns = []
    
    for col, ratio in missing_ratio.items():
        if ratio > 0.5:
            critical_columns.append((col, ratio))
        elif ratio > 0.2:  # 20%到50%之间的缺失值比例也值得警告
            warning_columns.append((col, ratio))
    
    # 生成验证结果
    validation_result = {
        'missing_ratio': missing_ratio.to_dict(),
        'critical_columns': critical_columns,
        'warning_columns': warning_columns,
        'total_rows': len(data_df),
        'valid_rows': len(data_df.dropna()),
        'is_valid': len(critical_columns) == 0
    }
    
    # 打印验证结果
    print("数据完整性验证结果:")
    print(f"总行数: {validation_result['total_rows']}")
    print(f"有效行数: {validation_result['valid_rows']}")
    print(f"有效数据比例: {validation_result['valid_rows'] / validation_result['total_rows']:.2%}")
    
    if critical_columns:
        print("严重缺失 (>50%):")
        for col, ratio in critical_columns:
            print(f"  - {col}: {ratio:.2%}")
    
    if warning_columns:
        print("警告缺失 (20%-50%):")
        for col, ratio in warning_columns:
            print(f"  - {col}: {ratio:.2%}")
    
    return validation_result['is_valid'], validation_result

def detect_outliers(data_df, method='zscore', threshold=3, columns=None):
    """
    检测数据中的异常值
    
    参数:
    data_df: 数据DataFrame
    method: 检测方法，'zscore'或'iqr'
    threshold: 阈值（z-score方法使用，通常为3）
    columns: 要检测的列，默认为所有数值列
    
    返回:
    包含异常值索引的字典，每个键是列名，值是该列中的异常值索引
    """
    # 如果未指定列，则使用所有数值列
    if columns is None:
        columns = data_df.select_dtypes(include=[np.number]).columns
    
    outliers_dict = {}
    
    for col in columns:
        # 确保该列是数值类型且不包含全部缺失值
        if col in data_df.columns and pd.api.types.is_numeric_dtype(data_df[col]) and not data_df[col].isna().all():
            # 获取非缺失值
            col_data = data_df[col].dropna()
            
            if method == 'zscore':
                # Z-score方法
                z_scores = np.abs((col_data - col_data.mean()) / col_data.std())
                outlier_indices = col_data[z_scores > threshold].index
            elif method == 'iqr':
                # IQR方法
                Q1 = col_data.quantile(0.25)
                Q3 = col_data.quantile(0.75)
                IQR = Q3 - Q1
                outlier_indices = col_data[(col_data < Q1 - 1.5 * IQR) | (col_data > Q3 + 1.5 * IQR)].index
            else:
                raise ValueError(f"不支持的异常值检测方法: {method}")
            
            if len(outlier_indices) > 0:
                outliers_dict[col] = outlier_indices.tolist()
    
    # 打印检测结果
    total_outliers = sum(len(indices) for indices in outliers_dict.values())
    if total_outliers > 0:
        print(f"检测到 {total_outliers} 个异常值:")
        for col, indices in outliers_dict.items():
            print(f"  - {col}: {len(indices)} 个异常值 ({len(indices) / len(data_df):.2%})")
    else:
        print("未检测到异常值")
    
    return outliers_dict

def handle_outliers(data_df, outliers_dict, method='clip'):
    """
    处理异常值
    
    参数:
    data_df: 数据DataFrame
    outliers_dict: 通过detect_outliers函数获取的异常值字典
    method: 处理方法，'clip'（限制在合理范围内）、'remove'（删除）或'impute'（填充）
    
    返回:
    处理后的DataFrame
    """
    # 创建副本，避免修改原始数据
    df = data_df.copy()
    
    # 统计处理的异常值数量
    processed_count = 0
    
    for col, indices in outliers_dict.items():
        if col in df.columns:
            if method == 'clip':
                # 获取正常值的范围
                normal_data = df.loc[~df.index.isin(indices), col]
                if len(normal_data) > 0:
                    # 计算上下限（使用均值±3倍标准差或95%置信区间）
                    lower_bound = max(normal_data.mean() - 3 * normal_data.std(), normal_data.quantile(0.025))
                    upper_bound = min(normal_data.mean() + 3 * normal_data.std(), normal_data.quantile(0.975))
                    
                    # 裁剪异常值
                    old_values = df.loc[indices, col].values
                    df.loc[indices, col] = np.clip(df.loc[indices, col], lower_bound, upper_bound)
                    processed_count += len(indices)
                    
                    print(f"列 {col} 中的 {len(indices)} 个异常值已被裁剪到范围 [{lower_bound:.2f}, {upper_bound:.2f}]")
                    
            elif method == 'remove':
                # 删除包含异常值的行
                df = df[~df.index.isin(indices)]
                processed_count += len(indices)
                print(f"已删除 {len(indices)} 行包含列 {col} 异常值的数据")
                
            elif method == 'impute':
                # 使用中位数填充异常值
                median_value = df[col].median()
                df.loc[indices, col] = median_value
                processed_count += len(indices)
                print(f"列 {col} 中的 {len(indices)} 个异常值已被中位数 {median_value:.2f} 替换")
    
    print(f"共处理了 {processed_count} 个异常值")
    return df

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='数据预处理脚本')
    parser.add_argument('--dataset', type=str, required=True, help='数据集名称（osic或adni）')
    parser.add_argument('--data_path', type=str, required=True, help='数据目录路径')
    
    args = parser.parse_args()
    
    if args.dataset.lower() == 'osic':
        # 处理OSIC数据集
        preprocessor = OSICDataPreprocessor(args.data_path)
        train_df, test_df = preprocessor.load_data()
        print(f"加载了OSIC数据集：{len(train_df)} 训练样本，{len(test_df)} 测试样本")
        
        # 预处理结构化数据
        train_df, test_df = preprocessor.preprocess_structured_data()
        print("结构化数据预处理完成")
        
        # 准备时间序列数据
        grouped_data = preprocessor.prepare_time_series_data()
        print(f"准备了 {len(grouped_data)} 个患者的时间序列数据")
        
        # 分割数据
        train_ids, val_ids, test_ids = preprocessor.split_data()
        print(f"数据分割完成：{len(train_ids)} 训练患者，{len(val_ids)} 验证患者，{len(test_ids)} 测试患者")
    
    elif args.dataset.lower() == 'adni':
        # 处理ADNI数据集
        preprocessor = ADNIDataPreprocessor(args.data_path)
        data_df = preprocessor.load_data()
        print(f"加载了ADNI数据集：{len(data_df)} 样本")
        
        # 预处理结构化数据
        data_df = preprocessor.preprocess_structured_data()
        print("结构化数据预处理完成")
        
        # 准备时间序列数据
        grouped_data = preprocessor.prepare_time_series_data()
        print(f"准备了 {len(grouped_data)} 个患者的时间序列数据")
    
    else:
        print(f"未知数据集：{args.dataset}") 