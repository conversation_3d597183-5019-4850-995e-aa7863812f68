import torch
import torch.nn as nn
from ..attention.fusion_strategies import FusionLayer, EmbeddingFusion

class MultimodalEmbedding(nn.Module):
    """
    处理结构化数据和图像数据的多模态嵌入模型
    
    参数:
    structured_dim (int): 结构化数据的维度
    structured_hidden_dim (int): 结构化数据嵌入的隐藏维度
    image_embed_dim (int): 图像嵌入的维度
    output_dim (int): 输出嵌入的维度
    fusion_type (str): 融合类型，'concat'或'sum'，默认'concat'
    dropout (float): dropout率，默认0.2
    """
    def __init__(self, structured_dim, structured_hidden_dim, image_embed_dim, output_dim, fusion_type='concat', dropout=0.2):
        super(MultimodalEmbedding, self).__init__()
        
        self.fusion_type = fusion_type
        
        # 结构化数据的嵌入层
        self.structured_embed = nn.Sequential(
            nn.Linear(structured_dim, structured_hidden_dim),
            nn.LayerNorm(structured_hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 图像特征的嵌入层（可选，用于对齐维度）
        if fusion_type == 'sum' and image_embed_dim != structured_hidden_dim:
            self.image_projection = nn.Linear(image_embed_dim, structured_hidden_dim)
            final_dim = structured_hidden_dim
        else:
            self.image_projection = nn.Identity()
            final_dim = image_embed_dim
        
        # 多模态融合层
        if fusion_type == 'concat':
            self.fusion_layer = FusionLayer(
                fusion_type='concat',
                input_dims=[structured_hidden_dim, final_dim],
                output_dim=output_dim
            )
        else:  # sum
            self.fusion_layer = FusionLayer(
                fusion_type='sum',
                input_dims=[structured_hidden_dim, final_dim]
            )
            
            # 如果使用加和融合，则可能需要额外的投影层调整维度
            if structured_hidden_dim != output_dim:
                self.output_projection = nn.Linear(structured_hidden_dim, output_dim)
            else:
                self.output_projection = nn.Identity()
    
    def forward(self, structured_data, image_embedding):
        """
        前向传播
        
        参数:
        structured_data: 结构化数据，形状为 [batch_size, seq_len, structured_dim]
        image_embedding: 图像嵌入，形状为 [batch_size, image_embed_dim] 或 [batch_size, seq_len, image_embed_dim]
        
        返回:
        多模态嵌入: 形状为 [batch_size, seq_len, output_dim]
        """
        # 嵌入结构化数据
        structured_embedding = self.structured_embed(structured_data)
        
        # 处理图像嵌入
        if len(image_embedding.shape) == 2:
            # 如果图像嵌入是 [batch_size, image_embed_dim]，则扩展为 [batch_size, seq_len, image_embed_dim]
            batch_size, image_dim = image_embedding.shape
            seq_len = structured_embedding.size(1)
            image_embedding = image_embedding.unsqueeze(1).expand(-1, seq_len, -1)
        
        # 如果需要，对图像嵌入进行维度调整
        image_embedding = self.image_projection(image_embedding)
        
        # 融合嵌入
        multimodal_embedding = self.fusion_layer([structured_embedding, image_embedding])
        
        # 对于加和融合，可能需要额外的维度调整
        if self.fusion_type == 'sum':
            multimodal_embedding = self.output_projection(multimodal_embedding)
        
        return multimodal_embedding

class MultimodalEncoder(nn.Module):
    """
    多模态编码器，整合图像编码器和结构化数据处理
    
    参数:
    structured_dim (int): 结构化数据的维度
    image_encoder (nn.Module): 图像编码器模型
    fusion_type (str): 融合类型，'sum'或'concat'，默认'concat'
    hidden_dim (int): 隐藏层维度，默认128
    output_dim (int): 输出嵌入维度，默认128
    dropout (float): dropout率，默认0.2
    """
    def __init__(self, structured_dim, image_encoder, fusion_type='concat', 
                 hidden_dim=128, output_dim=128, dropout=0.2):
        super(MultimodalEncoder, self).__init__()
        
        self.image_encoder = image_encoder
        self.image_embed_dim = image_encoder.image_encoder.projection[-1].out_features
        self.fusion_type = fusion_type
        
        # 创建多模态嵌入
        self.multimodal_embed = MultimodalEmbedding(
            structured_dim=structured_dim,
            structured_hidden_dim=hidden_dim,
            image_embed_dim=self.image_embed_dim,
            output_dim=output_dim,
            fusion_type=fusion_type,
            dropout=dropout
        )
    
    def forward(self, structured_data, images):
        """
        前向传播
        
        参数:
        structured_data: 结构化数据，形状为 [batch_size, seq_len, structured_dim]
        images: 图像数据，形状根据图像编码器的要求
        
        返回:
        多模态嵌入: 形状为 [batch_size, seq_len, output_dim]
        """
        # 编码图像
        image_embedding = self.image_encoder(images)
        
        # 融合图像和结构化数据
        multimodal_embedding = self.multimodal_embed(structured_data, image_embedding)
        
        return multimodal_embedding

class CrossModalAttentionFusion(nn.Module):
    """
    使用交叉模态注意力机制进行融合
    
    参数:
    structured_dim (int): 结构化数据的维度
    image_dim (int): 图像嵌入的维度
    output_dim (int): 输出嵌入的维度
    num_heads (int): 注意力头数量
    dropout (float): dropout率，默认0.1
    """
    def __init__(self, structured_dim, image_dim, output_dim, num_heads=4, dropout=0.1):
        super(CrossModalAttentionFusion, self).__init__()
        
        # 确保维度可以被头数量整除
        hidden_dim = (max(structured_dim, image_dim) // num_heads) * num_heads
        
        # 结构化数据投影
        self.structured_projection = nn.Linear(structured_dim, hidden_dim)
        
        # 图像数据投影
        self.image_projection = nn.Linear(image_dim, hidden_dim)
        
        # 交叉注意力层
        self.cross_attention = nn.MultiheadAttention(
            embed_dim=hidden_dim,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True
        )
        
        # 输出投影
        self.output_projection = nn.Sequential(
            nn.Linear(hidden_dim, output_dim),
            nn.LayerNorm(output_dim),
            nn.Dropout(dropout)
        )
        
        # LayerNorm 和 残差连接
        self.norm1 = nn.LayerNorm(hidden_dim)
        self.norm2 = nn.LayerNorm(hidden_dim)
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, structured_data, image_embedding):
        """
        前向传播
        
        参数:
        structured_data: 结构化数据，形状为 [batch_size, seq_len, structured_dim]
        image_embedding: 图像嵌入，形状为 [batch_size, image_embed_dim] 或 [batch_size, seq_len, image_embed_dim]
        
        返回:
        融合后的嵌入: 形状为 [batch_size, seq_len, output_dim]
        """
        # 投影结构化数据
        structured_proj = self.structured_projection(structured_data)
        
        # 处理图像嵌入
        if len(image_embedding.shape) == 2:
            # 如果图像嵌入是 [batch_size, image_embed_dim]，则扩展为 [batch_size, seq_len, image_embed_dim]
            batch_size, image_dim = image_embedding.shape
            seq_len = structured_data.size(1)
            image_embedding = image_embedding.unsqueeze(1).expand(-1, seq_len, -1)
        
        # 投影图像嵌入
        image_proj = self.image_projection(image_embedding)
        
        # 应用结构化数据到图像的注意力
        # 使用结构化数据作为查询，图像嵌入作为键和值
        attn_output, _ = self.cross_attention(
            query=structured_proj,
            key=image_proj,
            value=image_proj
        )
        
        # 残差连接和LayerNorm
        fusion = self.norm1(structured_proj + self.dropout(attn_output))
        
        # 输出投影
        output = self.output_projection(fusion)
        
        return output 