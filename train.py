import os
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import argparse
import yaml
import wandb
from tqdm import tqdm

from models.baseline.lstm import LSTMModelForecaster
from models.baseline.neural_cde import NeuralCDEForecaster
from models.multimodal.image_models import DICOMSliceEncoder
from models.realdiffusionnet import RealDiffFusionNetForecaster
from utils.data_preprocessing import create_dataloaders
from utils.evaluation import EvaluationTracker, evaluate_model
from utils.visualization import plot_training_curves, plot_metrics, plot_predictions

def setup_ddp(config):
    """
    分布式训练初始化
    
    参数:
    config: 配置字典
    
    返回:
    是否成功初始化
    """
    try:
        import torch.distributed as dist
        import os
        
        if config.get("use_ddp", False):
            # 获取环境变量
            rank = int(os.environ.get("RANK", 0))
            world_size = int(os.environ.get("WORLD_SIZE", 1))
            local_rank = int(os.environ.get("LOCAL_RANK", 0))
            
            # 设置设备
            torch.cuda.set_device(local_rank)
            
            # 初始化进程组
            dist.init_process_group(
                backend=config.get("ddp_backend", 'nccl'),
                init_method=config.get("ddp_init_method", 'env://'),
                world_size=world_size,
                rank=rank
            )
            
            # 打印分布式训练信息
            print(f"分布式训练初始化成功 - Rank: {rank}/{world_size}, Local Rank: {local_rank}")
            
            return True, local_rank, rank, world_size
        else:
            return False, 0, 0, 1
    except Exception as e:
        print(f"分布式训练初始化失败: {e}")
        return False, 0, 0, 1
        
def cleanup_ddp():
    """
    清理分布式训练环境
    """
    try:
        import torch.distributed as dist
        if dist.is_initialized():
            dist.destroy_process_group()
            print("分布式进程组已清理")
    except Exception as e:
        print(f"清理分布式训练环境时出错: {e}")

def train_model(model, train_loader, val_loader, criterion, optimizer,
                device, num_epochs=100, scheduler=None, patience=10,
                model_save_path=None, use_wandb=False, use_amp=False,
                pretrained_cde_path=None, use_reg_loss=False, reg_weight=0.1,
                config=None, model_type='realdiffusionnet'):
    """
    训练模型
    
    参数:
    model: 要训练的模型
    train_loader: 训练数据加载器
    val_loader: 验证数据加载器
    criterion: 损失函数
    optimizer: 优化器
    device: 设备（CPU或GPU）
    num_epochs: 训练轮数
    scheduler: 学习率调度器
    patience: 早停耐心值
    model_save_path: 模型保存路径
    use_wandb: 是否使用wandb跟踪实验
    use_amp: 是否使用混合精度训练
    pretrained_cde_path: 预训练CDE模型的路径
    use_reg_loss: 是否使用正则化损失
    reg_weight: 正则化损失的权重
    
    返回:
    训练好的模型和评估追踪器
    """
    # 初始化评估追踪器
    tracker = EvaluationTracker()
    
    # 早停变量
    best_val_loss = float('inf')
    patience_counter = 0
    
    # 初始化混合精度训练所需的梯度缩放器
    scaler = torch.cuda.amp.GradScaler() if use_amp else None
    
    # Load pre-trained CDE if provided
    if isinstance(model, RealDiffFusionNetForecaster) and pretrained_cde_path:
        model.model.load_pretrained_cde(pretrained_cde_path)
    
    # 训练循环
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_targets = []
        train_predictions = []
        
        for batch in tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs} [Train]"):
            # 准备数据
            features = batch['features'].to(device)
            targets = batch['target'].to(device)
            
            # 是否使用混合精度训练
            if use_amp:
                with torch.cuda.amp.autocast():
                    # 前向传播
                    lengths = batch.get('lengths', None)
                    if lengths is not None:
                        lengths = lengths.to(device)

                    if 'image' in batch and config.get('use_multimodal', False):
                        images = batch['image'].to(device)
                        if use_reg_loss:
                            outputs, reg_loss = model(features, lengths=lengths, images=images, return_reg_loss=True)
                            loss = criterion(outputs, targets) + reg_weight * reg_loss
                        else:
                            outputs = model(features, lengths=lengths, images=images)
                    else:
                        # 根据模型类型调用不同的forward方法
                        if model_type == 'realdiffusionnet':
                            outputs = model(features, lengths=lengths)
                        else:
                            outputs = model(features, lengths=lengths)

                    # 获取最后一个时间步的输出（如果需要）
                    if outputs.dim() > 2:
                        outputs = outputs[:, -1, :]

                    # 确保输出维度匹配目标维度
                    if outputs.dim() > 1 and outputs.size(1) == 1:
                        outputs = outputs.squeeze(1)

                    # 计算损失
                    loss = criterion(outputs, targets)
                
                # 反向传播和优化（使用AMP）
                optimizer.zero_grad()
                scaler.scale(loss).backward()
                scaler.step(optimizer)
                scaler.update()
            else:
                # 常规训练（不使用AMP）
                # 前向传播
                lengths = batch.get('lengths', None)
                if lengths is not None:
                    lengths = lengths.to(device)

                if 'image' in batch and config.get('use_multimodal', False):
                    images = batch['image'].to(device)
                    if use_reg_loss:
                        outputs, reg_loss = model(features, lengths=lengths, images=images, return_reg_loss=True)
                        loss = criterion(outputs, targets) + reg_weight * reg_loss
                    else:
                        outputs = model(features, lengths=lengths, images=images)
                else:
                    # 根据模型类型调用不同的forward方法
                    if model_type == 'realdiffusionnet':
                        outputs = model(features, lengths=lengths)
                    else:
                        outputs = model(features, lengths=lengths)
                    
                # 获取最后一个时间步的输出（如果需要）
                if outputs.dim() > 2:
                    outputs = outputs[:, -1, :]

                # 确保输出维度匹配目标维度
                if outputs.dim() > 1 and outputs.size(1) == 1:
                    outputs = outputs.squeeze(1)
                
                # 计算损失
                loss = criterion(outputs, targets)
                
                # 反向传播和优化
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
            
            # 累积损失
            train_loss += loss.item() * features.size(0)
            
            # 收集预测和目标
            train_targets.append(targets.cpu().numpy())
            train_predictions.append(outputs.detach().cpu().numpy())
        
        # 计算平均训练损失
        train_loss /= len(train_loader.dataset)
        
        # 合并所有批次的预测和目标
        train_targets = np.concatenate(train_targets)
        train_predictions = np.concatenate(train_predictions)
        
        # 更新训练指标
        train_metrics = tracker.update_train(train_loss, train_targets, train_predictions)
        
        # 评估验证数据
        val_loss, val_metrics = evaluate_model(model, val_loader, criterion, device)
        
        # 更新验证指标
        try:
            _, is_best = tracker.update_val(val_loss, val_metrics['y_true'], val_metrics['y_pred'], epoch)
        except KeyError:
            print("警告：评估指标中缺少y_true或y_pred，跳过验证指标更新")
            is_best = val_loss < best_val_loss
        
        # 打印验证结果
        print(f"  验证损失: {val_loss:.4f}")
        
        # 学习率调整
        if scheduler is not None:
            if isinstance(scheduler, optim.lr_scheduler.ReduceLROnPlateau):
                scheduler.step(val_loss)
            else:
                scheduler.step()
        
        # 记录到wandb
        if use_wandb:
            wandb.log({
                "epoch": epoch + 1,
                "train_loss": train_loss,
                "val_loss": val_loss,
                "train_rmse": train_metrics['rmse'],
                "val_rmse": val_metrics['rmse'],
                "train_mae": train_metrics['mae'],
                "val_mae": val_metrics['mae'],
                "train_r2": train_metrics['r2'],
                "val_r2": val_metrics['r2'],
                "learning_rate": optimizer.param_groups[0]['lr']
            })
        
        # 打印进度
        print(f"Epoch {epoch+1}/{num_epochs} - "
              f"Train Loss: {train_loss:.4f}, "
              f"Val Loss: {val_loss:.4f}, "
              f"Train RMSE: {train_metrics['rmse']:.4f}, "
              f"Val RMSE: {val_metrics['rmse']:.4f}")
        
        # 保存最佳模型
        if is_best and model_save_path:
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'train_loss': train_loss,
                'val_loss': val_loss,
                'train_metrics': train_metrics,
                'val_metrics': val_metrics
            }, model_save_path)
            print(f"Saved best model to {model_save_path}")
        
        # 早停
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
        else:
            patience_counter += 1
            if patience_counter >= patience:
                print(f"Early stopping at epoch {epoch+1}")
                break
    
    # 如果启用了wandb，保存最终模型
    if use_wandb:
        wandb.save(model_save_path)
    
    return model, tracker

def main():
    parser = argparse.ArgumentParser(description='训练RealDiffusionNet模型')
    parser.add_argument('--config', type=str, default='configs/realdiffusionnet.yaml', help='配置文件路径')
    parser.add_argument('--data_path', type=str, default='data/osic', help='数据目录路径')
    parser.add_argument('--output_dir', type=str, default='output', help='输出目录路径')
    parser.add_argument('--use_wandb', action='store_true', help='是否使用wandb追踪')
    parser.add_argument('--local_rank', type=int, default=-1, help='分布式训练的本地进程号')
    parser.add_argument('--use_amp', action='store_true', help='是否启用混合精度训练')
    args = parser.parse_args()
    
    # 加载配置
    with open(args.config, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 更新配置
    config['data_path'] = args.data_path
    config['output_dir'] = args.output_dir
    
    # 处理混合精度训练参数
    if args.use_amp:
        config['use_amp'] = True
    
    # 验证混合精度训练是否可用
    if config.get('use_amp', False):
        if not torch.cuda.is_available():
            print("警告: 混合精度训练需要CUDA支持，但未检测到CUDA。已禁用混合精度训练。")
            config['use_amp'] = False
        else:
            print("混合精度训练已启用，可加速训练并减少显存使用。")
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 设置随机种子
    seed = config.get('seed', 42)
    torch.manual_seed(seed)
    np.random.seed(seed)
    
    # 初始化分布式训练（如果启用）
    is_ddp, local_rank, rank, world_size = setup_ddp(config)
    
    # 设置设备
    if is_ddp:
        # 在分布式训练中，使用local_rank指定的GPU
        device = torch.device(f"cuda:{local_rank}")
    else:
        # 否则使用普通设置
        device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    
    print(f"使用设备: {device}")
    
    # 创建数据加载器
    train_loader, val_loader, test_loader = create_dataloaders(
        dataset_name=config['dataset'],
        data_dir=config['data_path'],
        batch_size=config['batch_size'],
        use_images=config.get('use_images', True),
        max_seq_len=config.get('max_seq_len'),
        task=config.get('task', 'regression')
    )
    
    # 如果使用DDP，则包装数据加载器
    if is_ddp:
        from torch.utils.data.distributed import DistributedSampler
        train_sampler = DistributedSampler(train_loader.dataset)
        train_loader = torch.utils.data.DataLoader(
            train_loader.dataset,
            batch_size=config['batch_size'],
            sampler=train_sampler,
            num_workers=config.get('num_workers', 4),
            pin_memory=True
        )
    
    # 创建图像编码器
    if config.get('use_images', True) and config.get('use_multimodal', True):
        image_encoder = DICOMSliceEncoder(
            output_dim=config.get('image_embed_dim', 128),
            pretrained=config.get('image_pretrained', True),
            num_slices=config.get('num_slices', 1)
        ).to(device)
        image_embed_dim = config.get('image_embed_dim', 128)
    else:
        image_encoder = None
        image_embed_dim = 0
    
    # 创建模型
    model_type = config.get('model_type', 'realdiffusionnet')
    
    # 获取输入维度（从数据集中推断）
    sample_batch = next(iter(train_loader))
    input_dim = sample_batch['features'].shape[2]
    
    # 获取输出维度（从任务类型推断）
    task = config.get('task', 'regression')
    if task == 'regression':
        output_dim = 1
    else:  # classification
        num_classes = config.get('classification', {}).get('num_classes', 3)
        output_dim = num_classes
    
    # 根据模型类型创建模型
    if model_type == 'lstm':
        model = LSTMModelForecaster(
            input_dim=input_dim,
            hidden_dim=config['hidden_dim'],
            output_dim=output_dim,
            num_layers=config.get('num_layers', 2),
            dropout=config.get('dropout', 0.1)
        )
    elif model_type == 'neural_cde':
        model = NeuralCDEForecaster(
            input_channels=input_dim,
            hidden_channels=config['hidden_dim'],
            output_channels=output_dim,
            interpolation=config.get('interpolation', 'cubic')
        )
    else:  # realdiffusionnet
        model = RealDiffFusionNetForecaster(
            input_channels=input_dim,
            hidden_channels=config['hidden_dim'],
            image_embed_dim=image_embed_dim,
            output_dim=output_dim,
            attention_dim=config.get('attention_dim', 128),
            num_heads=config.get('num_heads', 4),
            ff_dim=config.get('ff_dim', 256),
            num_layers=config.get('num_layers', 2),
            dropout=config.get('dropout', 0.1),
            fusion_type=config.get('fusion_type', 'concat'),
            interpolation=config.get('interpolation', 'rectilinear')
        )
    
    # 移动模型到指定设备
    model = model.to(device)
    
    # 如果启用分布式训练，将模型包装为DDP模型
    if is_ddp:
        from torch.nn.parallel import DistributedDataParallel as DDP
        model = DDP(
            model, 
            device_ids=[local_rank], 
            output_device=local_rank,
            find_unused_parameters=True
        )
    
    # 设置损失函数
    if task == 'regression':
        criterion = nn.MSELoss()
    else:  # classification
        criterion = nn.CrossEntropyLoss()
    
    # 创建优化器
    optimizer = optim.Adam(
        model.parameters(),
        lr=config['lr'],
        weight_decay=config.get('weight_decay', 0.0001)
    )
    
    # 创建学习率调度器
    if config.get('use_lr_scheduler', False):
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='min',
            factor=0.5,
            patience=5,
            verbose=True
        )
    else:
        scheduler = None
    
    # 模型保存路径
    model_save_path = os.path.join(
        args.output_dir,
        f"{model_type}_{config['dataset']}.pt"
    )
    
    # 初始化wandb
    if args.use_wandb and (not is_ddp or rank == 0):
        import wandb
        wandb.init(
            project=config.get('wandb_project', 'realdiffusionnet'),
            group=config.get('wandb_group', config['dataset']),
            config=config
        )
    
    # 训练模型
    model, tracker = train_model(
        model=model,
        train_loader=train_loader,
        val_loader=val_loader,
        criterion=criterion,
        optimizer=optimizer,
        device=device,
        num_epochs=config['num_epochs'],
        scheduler=scheduler,
        patience=config.get('patience', 10),
        model_save_path=model_save_path,
        use_wandb=args.use_wandb,
        use_amp=config.get('use_amp', False), # 添加混合精度训练参数
        pretrained_cde_path=config.get('pretrained_cde_path'), # 添加预训练CDE路径
        use_reg_loss=config.get('use_reg_loss', False), # 添加正则化损失参数
        reg_weight=config.get('reg_weight', 0.1), # 添加正则化损失权重
        config=config,
        model_type=model_type
    )
    
    # 只在主进程上执行评估和保存
    if not is_ddp or rank == 0:
        # 输出训练结果
        summary = tracker.summarize()
        print("\n训练完成！最终指标:")
        
        for phase in ['train', 'val', 'test']:
            if summary[phase] is not None:
                print(f"\n{phase.capitalize()} 指标:")
                for metric_name, value in summary[phase].items():
                    try:
                        print(f"  {metric_name}: {value:.4f}")
                    except (TypeError, ValueError):
                        print(f"  {metric_name}: {value}")
        
        print(f"\n最佳验证指标 (轮次 {tracker.best_epoch}):")
        if summary['best_val'] is not None:
            for metric_name, value in summary['best_val'].items():
                try:
                    print(f"  {metric_name}: {value:.4f}")
                except (TypeError, ValueError):
                    print(f"  {metric_name}: {value}")
        
        # 绘制训练曲线
        plot_training_curves(
            tracker.train_losses,
            tracker.val_losses,
            title=f'{model_type} 在 {config["dataset"]} 上的训练和验证损失',
            save_path=os.path.join(args.output_dir, f"{model_type}_{config['dataset']}_loss.png")
        )
        
        # 如果有测试数据，在测试集上评估
        if test_loader is not None:
            print("\n在测试集上评估...")
            test_loss, test_metrics = evaluate_model(model, test_loader, criterion, device)
            
            print("测试指标:")
            for metric_name, value in test_metrics.items():
                print(f"  {metric_name}: {value:.4f}")
        
        # 导出模型为ONNX格式（可选）
        if config.get('export_onnx', False):
            from evaluate import export_model
            
            # 准备输出路径
            onnx_save_path = os.path.join(
                args.output_dir,
                f"{model_type}_{config['dataset']}.onnx"
            )
            
            # 推断输入形状
            input_shape = (1, 10, input_dim)  # (batch_size, seq_len, input_channels)
            
            # 图像输入形状（如果使用图像）
            image_shape = (1, 3, 224, 224) if config.get('use_images', True) else None
            
            # 导出模型
            export_model(model, onnx_save_path, input_shape, image_shape)
    
    # 清理分布式训练环境
    if is_ddp:
        cleanup_ddp()

if __name__ == "__main__":
    main() 