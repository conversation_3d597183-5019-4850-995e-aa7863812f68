# 简化Neural CDE配置文件（测试用）

# 模型配置
model_type: neural_cde
hidden_dim: 64
input_dim: null  # 自动根据数据集确定
output_dim: null  # 自动根据任务确定
interpolation: linear  # 改为'linear'插值方法
use_multimodal: false
# Neural CDE特定参数
cde_embedding_dim: 32
num_cde_layers: 1
solver: 'rk4'
adjoint: false
return_sequences: true

# 插值参数
interpolation_params:
  use_time: true
  spline_degree: 3
  smoothness: 0.1
  time_scaling: 1.0
  boundary_condition: 'not-a-knot'
  extrapolate: false

# 训练配置
batch_size: 4
num_epochs: 2
lr: 0.001
weight_decay: 0.0001
patience: 3
max_seq_len: null
seed: 42
use_early_stopping: true
use_amp: false

# 数据配置
dataset: osic
data_path: data/osic
use_images: false
task: regression

# 输出配置
output_dir: output
use_wandb: false 