import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import torch
import seaborn as sns
from matplotlib.font_manager import FontProperties

# 设置中文字体（如果可用）
try:
    font = FontProperties(fname=r"c:\windows\fonts\simsun.ttc", size=14)
except:
    font = None

def set_plot_style():
    """设置绘图样式"""
    plt.style.use('seaborn-v0_8-whitegrid')
    plt.rcParams['figure.figsize'] = (12, 8)
    plt.rcParams['font.size'] = 12
    plt.rcParams['axes.labelsize'] = 14
    plt.rcParams['axes.titlesize'] = 16
    plt.rcParams['xtick.labelsize'] = 12
    plt.rcParams['ytick.labelsize'] = 12
    plt.rcParams['legend.fontsize'] = 12
    plt.rcParams['axes.titleweight'] = 'bold'

def plot_training_curves(train_losses, val_losses, title='训练和验证损失曲线', 
                         save_path=None, show_plot=True):
    """
    绘制训练和验证损失曲线
    
    参数:
    train_losses: 训练损失列表
    val_losses: 验证损失列表
    title: 图表标题
    save_path: 保存路径，如果为None则不保存
    show_plot: 是否显示图表
    """
    set_plot_style()
    
    plt.figure()
    epochs = list(range(1, len(train_losses) + 1))
    
    plt.plot(epochs, train_losses, 'b-', label='训练损失')
    plt.plot(epochs, val_losses, 'r-', label='验证损失')
    
    plt.title(title, fontproperties=font)
    plt.xlabel('轮次 (Epoch)', fontproperties=font)
    plt.ylabel('损失 (Loss)', fontproperties=font)
    plt.grid(True)
    plt.legend(prop=font)
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    if show_plot:
        plt.show()
    else:
        plt.close()

def plot_metrics(metrics_dict, title='模型评估指标', save_path=None, show_plot=True):
    """
    绘制评估指标
    
    参数:
    metrics_dict: 评估指标字典，包含 'train', 'val', 'test' 键
    title: 图表标题
    save_path: 保存路径，如果为None则不保存
    show_plot: 是否显示图表
    """
    set_plot_style()
    
    # 提取指标
    phases = list(metrics_dict.keys())
    metrics = list(metrics_dict[phases[0]].keys())
    
    # 创建数据框
    data = []
    for phase in phases:
        if metrics_dict[phase] is not None:
            for metric in metrics:
                data.append({
                    '阶段': phase,
                    '指标': metric,
                    '值': metrics_dict[phase][metric]
                })
    
    df = pd.DataFrame(data)
    
    # 绘制条形图
    plt.figure(figsize=(12, 8))
    sns.barplot(x='指标', y='值', hue='阶段', data=df)
    
    plt.title(title, fontproperties=font)
    plt.xlabel('指标', fontproperties=font)
    plt.ylabel('值', fontproperties=font)
    plt.grid(True)
    plt.legend(title='阶段', prop=font)
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    if show_plot:
        plt.show()
    else:
        plt.close()

def plot_predictions(y_true, y_pred, title='预测值与真实值比较', 
                     save_path=None, show_plot=True):
    """
    绘制预测值与真实值散点图
    
    参数:
    y_true: 真实值（numpy数组或PyTorch张量）
    y_pred: 预测值（numpy数组或PyTorch张量）
    title: 图表标题
    save_path: 保存路径，如果为None则不保存
    show_plot: 是否显示图表
    """
    set_plot_style()
    
    if isinstance(y_true, torch.Tensor):
        y_true = y_true.detach().cpu().numpy()
    if isinstance(y_pred, torch.Tensor):
        y_pred = y_pred.detach().cpu().numpy()
    
    plt.figure()
    plt.scatter(y_true, y_pred, alpha=0.6)
    
    # 添加对角线
    min_val = min(np.min(y_true), np.min(y_pred))
    max_val = max(np.max(y_true), np.max(y_pred))
    plt.plot([min_val, max_val], [min_val, max_val], 'r--')
    
    plt.title(title, fontproperties=font)
    plt.xlabel('真实值', fontproperties=font)
    plt.ylabel('预测值', fontproperties=font)
    plt.grid(True)
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    if show_plot:
        plt.show()
    else:
        plt.close()

def plot_disease_progression(patient_data, predictions, title='疾病进展预测', 
                             save_path=None, show_plot=True):
    """
    绘制疾病进展曲线
    
    参数:
    patient_data: 患者历史数据，字典，包含 'weeks' 和 'fvc' 键
    predictions: 预测数据，字典，包含 'weeks' 和 'fvc' 键
    title: 图表标题
    save_path: 保存路径，如果为None则不保存
    show_plot: 是否显示图表
    """
    set_plot_style()
    
    plt.figure()
    
    # 绘制历史数据点
    plt.scatter(patient_data['weeks'], patient_data['fvc'], 
                color='blue', label='历史数据', s=50)
    
    # 绘制历史趋势线
    plt.plot(patient_data['weeks'], patient_data['fvc'], 
             'b-', alpha=0.5)
    
    # 绘制预测点
    plt.scatter(predictions['weeks'], predictions['fvc'], 
                color='red', label='预测数据', s=50)
    
    # 绘制预测趋势线
    plt.plot(predictions['weeks'], predictions['fvc'], 
             'r-', alpha=0.5)
    
    plt.title(title, fontproperties=font)
    plt.xlabel('周数', fontproperties=font)
    plt.ylabel('FVC值', fontproperties=font)
    plt.grid(True)
    plt.legend(prop=font)
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    if show_plot:
        plt.show()
    else:
        plt.close()

def plot_attention_weights(attention_weights, tokens=None, title='注意力权重', 
                           save_path=None, show_plot=True):
    """
    绘制注意力权重热力图
    
    参数:
    attention_weights: 注意力权重矩阵，形状为 [seq_len, seq_len]
    tokens: 标记列表，用于坐标轴标签
    title: 图表标题
    save_path: 保存路径，如果为None则不保存
    show_plot: 是否显示图表
    """
    set_plot_style()
    
    plt.figure(figsize=(10, 8))
    
    # 绘制热力图
    sns.heatmap(attention_weights, annot=False, cmap='viridis',
                xticklabels=tokens, yticklabels=tokens)
    
    plt.title(title, fontproperties=font)
    plt.xlabel('时间步 (目标)', fontproperties=font)
    plt.ylabel('时间步 (源)', fontproperties=font)
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    if show_plot:
        plt.show()
    else:
        plt.close()

def plot_model_comparison(model_results, metric='rmse', title='模型性能比较', 
                          save_path=None, show_plot=True):
    """
    绘制模型比较条形图
    
    参数:
    model_results: 字典，键为模型名称，值为包含指标的字典
    metric: 要比较的指标
    title: 图表标题
    save_path: 保存路径，如果为None则不保存
    show_plot: 是否显示图表
    """
    set_plot_style()
    
    models = list(model_results.keys())
    metric_values = [model_results[model][metric] for model in models]
    
    plt.figure(figsize=(12, 8))
    
    # 水平条形图
    y_pos = np.arange(len(models))
    plt.barh(y_pos, metric_values, align='center', alpha=0.7)
    plt.yticks(y_pos, models)
    
    plt.title(title, fontproperties=font)
    plt.xlabel(f'{metric.upper()}', fontproperties=font)
    plt.ylabel('模型', fontproperties=font)
    plt.grid(True)
    
    # 在每个条上添加数值标签
    for i, v in enumerate(metric_values):
        plt.text(v + 0.01, i, f'{v:.4f}', va='center')
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    if show_plot:
        plt.show()
    else:
        plt.close()

def plot_feature_importance(feature_names, importance_scores, title='特征重要性', 
                            save_path=None, show_plot=True):
    """
    绘制特征重要性条形图
    
    参数:
    feature_names: 特征名称列表
    importance_scores: 重要性分数列表
    title: 图表标题
    save_path: 保存路径，如果为None则不保存
    show_plot: 是否显示图表
    """
    set_plot_style()
    
    # 按重要性排序
    sorted_idx = np.argsort(importance_scores)
    sorted_features = [feature_names[i] for i in sorted_idx]
    sorted_scores = [importance_scores[i] for i in sorted_idx]
    
    plt.figure(figsize=(12, 8))
    
    # 水平条形图
    y_pos = np.arange(len(sorted_features))
    plt.barh(y_pos, sorted_scores, align='center', alpha=0.7)
    plt.yticks(y_pos, sorted_features)
    
    plt.title(title, fontproperties=font)
    plt.xlabel('重要性分数', fontproperties=font)
    plt.ylabel('特征', fontproperties=font)
    plt.grid(True)
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    if show_plot:
        plt.show()
    else:
        plt.close()

def save_confusion_matrix(cm, class_names, title='混淆矩阵', 
                         save_path=None, show_plot=True):
    """
    保存混淆矩阵
    
    参数:
    cm: 混淆矩阵
    class_names: 类别名称列表
    title: 图表标题
    save_path: 保存路径，如果为None则不保存
    show_plot: 是否显示图表
    """
    set_plot_style()
    
    plt.figure(figsize=(10, 8))
    
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=class_names, yticklabels=class_names)
    
    plt.title(title, fontproperties=font)
    plt.xlabel('预测标签', fontproperties=font)
    plt.ylabel('真实标签', fontproperties=font)
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    if show_plot:
        plt.show()
    else:
        plt.close() 

def plot_confusion_matrix(y_true, y_pred, classes=None, normalize=False, title='混淆矩阵', 
                       save_path=None, show_plot=True, cmap=plt.cm.Blues):
    """
    绘制分类混淆矩阵
    
    参数:
    y_true: 真实标签（numpy数组或PyTorch张量）
    y_pred: 预测标签（numpy数组或PyTorch张量）
    classes: 类别标签列表，如果为None则使用数字标签
    normalize: 是否将混淆矩阵归一化为百分比
    title: 图表标题
    save_path: 保存路径，如果为None则不保存
    show_plot: 是否显示图表
    cmap: 颜色映射
    
    返回:
    混淆矩阵（numpy数组）
    """
    from sklearn.metrics import confusion_matrix
    import itertools
    
    set_plot_style()
    
    if isinstance(y_true, torch.Tensor):
        y_true = y_true.detach().cpu().numpy()
    
    if isinstance(y_pred, torch.Tensor):
        if y_pred.dim() > 1 and y_pred.size(1) > 1:
            # 多类别分类，获取最大概率的类别索引
            y_pred = torch.argmax(y_pred, dim=1).detach().cpu().numpy()
        else:
            y_pred = y_pred.detach().cpu().numpy()
    
    # 对于二分类，确保预测值为0/1
    if len(np.unique(y_true)) == 2:
        y_pred = (y_pred > 0.5).astype(int)
    
    # 计算混淆矩阵
    cm = confusion_matrix(y_true, y_pred)
    
    # 如果未提供类别名称，使用默认的类别索引
    if classes is None:
        n_classes = len(np.unique(np.concatenate([y_true, y_pred])))
        if n_classes == 3:
            # ADNI数据集的典型类别：CN（认知正常）, MCI（轻度认知障碍）, AD（阿尔茨海默病）
            classes = ['CN', 'MCI', 'AD']
        else:
            classes = [str(i) for i in range(n_classes)]
    
    # 如果需要，归一化混淆矩阵
    if normalize:
        cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
        fmt = '.2f'
        title = title + " (标准化)"
    else:
        fmt = 'd'
    
    # 创建图表
    plt.figure(figsize=(10, 8))
    
    # 绘制混淆矩阵
    plt.imshow(cm, interpolation='nearest', cmap=cmap)
    plt.title(title, fontproperties=font)
    plt.colorbar()
    
    # 添加类别标签
    tick_marks = np.arange(len(classes))
    plt.xticks(tick_marks, classes, rotation=45)
    plt.yticks(tick_marks, classes)
    
    # 添加数值标签
    thresh = cm.max() / 2.
    for i, j in itertools.product(range(cm.shape[0]), range(cm.shape[1])):
        plt.text(j, i, format(cm[i, j], fmt),
                 horizontalalignment="center",
                 color="white" if cm[i, j] > thresh else "black")
    
    plt.tight_layout()
    plt.ylabel('真实标签', fontproperties=font)
    plt.xlabel('预测标签', fontproperties=font)
    
    # 保存或显示图表
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    if show_plot:
        plt.show()
    else:
        plt.close()
    
    return cm 

def plot_cognitive_trajectories(patient_data, predictions, metric_name='MMSE', 
                            title='认知评分轨迹预测', save_path=None, show_plot=True):
    """
    绘制ADNI患者认知评分轨迹图
    
    参数:
    patient_data: 患者历史数据，字典，包含 'months' 和 metric_name 键
    predictions: 预测数据，字典，包含 'months' 和 metric_name 键
    metric_name: 认知评分指标名称，如'MMSE'、'CDRSB'等
    title: 图表标题
    save_path: 保存路径，如果为None则不保存
    show_plot: 是否显示图表
    
    返回:
    None
    """
    set_plot_style()
    
    plt.figure(figsize=(14, 8))
    
    # 绘制历史数据点
    plt.scatter(patient_data['months'], patient_data[metric_name], 
                color='blue', label='历史数据', s=60, alpha=0.8)
    
    # 绘制历史趋势线
    plt.plot(patient_data['months'], patient_data[metric_name], 
             'b-', alpha=0.6, linewidth=2)
    
    # 绘制预测点
    plt.scatter(predictions['months'], predictions[metric_name], 
                color='red', label='预测数据', s=60, alpha=0.8)
    
    # 绘制预测趋势线
    plt.plot(predictions['months'], predictions[metric_name], 
             'r--', alpha=0.6, linewidth=2)
    
    # 根据认知评分类型设置y轴范围和标签
    if metric_name == 'MMSE':
        # MMSE评分范围是0-30，值越高表示认知状态越好
        plt.ylim(0, 30)
        plt.axhspan(24, 30, alpha=0.2, color='green', label='认知正常 (CN)')
        plt.axhspan(19, 24, alpha=0.2, color='yellow', label='轻度认知障碍 (MCI)')
        plt.axhspan(0, 19, alpha=0.2, color='red', label='阿尔茨海默病 (AD)')
    elif metric_name == 'CDRSB':
        # CDR-SB评分范围是0-18，值越高表示认知障碍越严重
        plt.ylim(0, 18)
        plt.axhspan(0, 2.5, alpha=0.2, color='green', label='认知正常 (CN)')
        plt.axhspan(2.5, 4.5, alpha=0.2, color='yellow', label='轻度认知障碍 (MCI)')
        plt.axhspan(4.5, 18, alpha=0.2, color='red', label='阿尔茨海默病 (AD)')
    elif metric_name == 'ADAS13':
        # ADAS13评分范围是0-85，值越高表示认知障碍越严重
        plt.ylim(0, 85)
        plt.axhspan(0, 15, alpha=0.2, color='green', label='认知正常 (CN)')
        plt.axhspan(15, 30, alpha=0.2, color='yellow', label='轻度认知障碍 (MCI)')
        plt.axhspan(30, 85, alpha=0.2, color='red', label='阿尔茨海默病 (AD)')
    
    # 添加参考线和图例
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend(loc='best', fontsize=12)
    
    # 设置标题和标签
    plt.title(f"{title} - {metric_name}", fontproperties=font, fontsize=16)
    plt.xlabel('月份', fontproperties=font, fontsize=14)
    plt.ylabel(f'{metric_name} 评分', fontproperties=font, fontsize=14)
    
    # 添加注释
    x_text = min(patient_data['months']) + (max(patient_data['months']) - min(patient_data['months'])) * 0.05
    y_text = max(patient_data[metric_name]) * 0.9
    
    if metric_name == 'MMSE':
        plt.text(x_text, y_text, "MMSE: 值越高表示认知状态越好",
                 fontproperties=font, fontsize=12)
    elif metric_name in ['CDRSB', 'ADAS13']:
        plt.text(x_text, y_text, f"{metric_name}: 值越高表示认知障碍越严重",
                 fontproperties=font, fontsize=12)
    
    # 保存或显示图表
    if save_path:
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    if show_plot:
        plt.show()
    else:
        plt.close() 