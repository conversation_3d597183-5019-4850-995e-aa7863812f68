# RealDiffFusionNet项目依赖
# 使用国内镜像源安装: pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple

# 基础依赖
numpy==1.22.4
pandas==1.5.3
scikit-learn==1.2.2
matplotlib==3.7.1
seaborn==0.12.2
tqdm==4.65.0

# 深度学习框架
# 注意：以下 torch 和 torchvision 是CPU版本。
# 如果您有NVIDIA GPU，请不要直接安装这两行。
# 请根据下面的GPU安装步骤来操作。
# torch==1.13.1
# torchvision==0.14.1
torchdiffeq==0.2.3
torchcde==0.2.5

# 图像处理
opencv-python==********
pillow==9.5.0
efficientnet-pytorch==0.7.1

# 实验跟踪
wandb==0.15.0

# 其他工具
jupyterlab==3.6.3
ipywidgets==8.0.6 