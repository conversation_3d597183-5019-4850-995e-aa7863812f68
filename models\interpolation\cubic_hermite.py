import torch
import torch.nn as nn
import torchcde

class CubicHermiteInterpolation:
    """
    立方Hermite样条插值，用于处理不规则采样的时间序列数据
    
    这种插值方法使用微分方程可以处理的平滑曲线，适合疾病进展建模中的连续数据
    同时保留了因果性，确保在任何给定点的插值仅受到先前时间点的影响，不包括未来数据
    """
    
    @staticmethod
    def interpolate(x, times=None, backward_differences=True):
        """
        对输入数据进行立方Hermite样条插值
        
        参数:
        x (tensor): 输入数据，形状为 [batch_size, seq_len, channels]
        times (tensor): 时间点，形状为 [seq_len]，如果为None则使用等间隔时间点
        backward_differences (bool): 是否使用反向差分以确保因果性，默认为True
        
        返回:
        coeffs: 插值系数，可用于torchcde的CubicSpline
        """
        batch_size, seq_len, channels = x.size()
        
        # 如果未提供时间点，则使用等间隔时间点
        if times is None:
            times = torch.linspace(0, 1, seq_len).to(x.device)
        
        # 使用torchcde计算自然立方样条系数
        if backward_differences:
            # 使用反向差分以确保因果性
            coeffs = torchcde.hermite_cubic_coefficients_with_backward_differences(x, times)
        else:
            # 使用标准差分（非因果）
            coeffs = torchcde.natural_cubic_spline_coeffs(x, times)
        
        return coeffs
    
    @staticmethod
    def get_interpolator(coeffs):
        """
        获取基于系数的插值器对象
        
        参数:
        coeffs: 由interpolate方法生成的系数
        
        返回:
        插值器: CubicSpline对象，可用于在任意时间点评估和求导
        """
        return torchcde.CubicSpline(coeffs)
    
    @staticmethod
    def evaluate(interpolator, t):
        """
        在给定时间点评估插值
        
        参数:
        interpolator: 插值器对象
        t: 要评估的时间点
        
        返回:
        插值值: 在时间点t的插值
        """
        return interpolator.evaluate(t)
    
    @staticmethod
    def derivative(interpolator, t):
        """
        在给定时间点计算导数
        
        参数:
        interpolator: 插值器对象
        t: 要计算导数的时间点
        
        返回:
        导数值: 在时间点t的导数
        """
        return interpolator.derivative(t) 