import torch
import torch.nn as nn
import torch.nn.functional as F
import math

class MultiHeadAttention(nn.Module):
    """
    多头注意力机制
    
    参数:
    d_model (int): 输入和输出的维度
    num_heads (int): 注意力头的数量
    dropout (float): dropout率，默认0.1
    """
    def __init__(self, d_model, num_heads, dropout=0.1):
        super(MultiHeadAttention, self).__init__()
        # 确保维度可以被头数整除
        assert d_model % num_heads == 0
        
        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = d_model // num_heads  # 每个头的维度
        
        # 线性层用于query、key、value和输出的变换
        self.W_q = nn.Linear(d_model, d_model)
        self.W_k = nn.Linear(d_model, d_model)
        self.W_v = nn.Linear(d_model, d_model)
        self.W_o = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
    
    def scaled_dot_product_attention(self, Q, K, V, mask=None):
        """
        计算缩放点积注意力
        
        参数:
        Q: Query矩阵，形状为[batch_size, num_heads, seq_len, d_k]
        K: Key矩阵，形状为[batch_size, num_heads, seq_len, d_k]
        V: Value矩阵，形状为[batch_size, num_heads, seq_len, d_k]
        mask: 掩码，形状为[batch_size, 1, seq_len, seq_len]，默认为None
        
        返回:
        注意力输出和注意力权重
        """
        # 计算注意力得分
        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.d_k)
        
        # 应用掩码（如果提供）
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        
        # 计算注意力权重
        attn_weights = F.softmax(scores, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        # 应用注意力权重到值
        output = torch.matmul(attn_weights, V)
        
        return output, attn_weights
    
    def forward(self, query, key, value, mask=None):
        """
        前向传播
        
        参数:
        query: 查询张量，形状为[batch_size, seq_len, d_model]
        key: 键张量，形状为[batch_size, seq_len, d_model]
        value: 值张量，形状为[batch_size, seq_len, d_model]
        mask: 掩码，形状为[batch_size, 1, seq_len, seq_len]，默认为None
        
        返回:
        注意力机制的输出
        """
        batch_size = query.size(0)
        
        # 线性变换
        Q = self.W_q(query)
        K = self.W_k(key)
        V = self.W_v(value)
        
        # 重塑为多头形状
        Q = Q.view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        K = K.view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        V = V.view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        
        # 应用注意力
        output, attn_weights = self.scaled_dot_product_attention(Q, K, V, mask)
        
        # 连接多头的结果
        output = output.transpose(1, 2).contiguous().view(batch_size, -1, self.d_model)
        
        # 最终线性变换
        output = self.W_o(output)
        
        return output, attn_weights

class CausalMultiHeadAttention(nn.Module):
    """
    因果多头注意力机制，确保每个时间步只能关注自身及之前的时间步
    
    参数:
    d_model (int): 输入和输出的维度
    num_heads (int): 注意力头的数量
    dropout (float): dropout率，默认0.1
    """
    def __init__(self, d_model, num_heads, dropout=0.1):
        super(CausalMultiHeadAttention, self).__init__()
        self.attention = MultiHeadAttention(d_model, num_heads, dropout)
    
    def forward(self, query, key, value):
        """
        前向传播，应用因果掩码
        
        参数:
        query: 查询张量，形状为[batch_size, seq_len, d_model]
        key: 键张量，形状为[batch_size, seq_len, d_model]
        value: 值张量，形状为[batch_size, seq_len, d_model]
        
        返回:
        注意力机制的输出
        """
        seq_len = query.size(1)
        
        # 创建因果掩码（下三角矩阵）
        mask = torch.triu(
            torch.ones(seq_len, seq_len), diagonal=1
        ).bool().to(query.device)
        mask = mask.unsqueeze(0).unsqueeze(1)  # [1, 1, seq_len, seq_len]
        
        # 注意：我们需要反转掩码，因为masked_fill填充的是mask==0的位置
        causal_mask = ~mask
        
        return self.attention(query, key, value, causal_mask)

class AttentionBlock(nn.Module):
    """
    注意力块，包含多头注意力、层归一化和前馈神经网络
    
    参数:
    d_model (int): 输入和输出的维度
    num_heads (int): 注意力头的数量
    ff_dim (int): 前馈神经网络的隐藏层维度
    dropout (float): dropout率，默认0.1
    causal (bool): 是否使用因果掩码，默认True
    """
    def __init__(self, d_model, num_heads, ff_dim, dropout=0.1, causal=True):
        super(AttentionBlock, self).__init__()
        
        # 注意力层
        if causal:
            self.attention = CausalMultiHeadAttention(d_model, num_heads, dropout)
        else:
            self.attention = MultiHeadAttention(d_model, num_heads, dropout)
        
        # 层归一化
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        
        # 前馈神经网络
        self.feed_forward = nn.Sequential(
            nn.Linear(d_model, ff_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(ff_dim, d_model)
        )
        
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, x):
        """
        前向传播
        
        参数:
        x: 输入张量，形状为[batch_size, seq_len, d_model]
        
        返回:
        注意力块的输出
        """
        # 多头注意力
        attn_output, _ = self.attention(x, x, x)
        attn_output = self.dropout(attn_output)
        
        # 残差连接和层归一化
        x = self.norm1(x + attn_output)
        
        # 前馈神经网络
        ff_output = self.feed_forward(x)
        
        # 残差连接和层归一化
        x = self.norm2(x + ff_output)
        
        return x 