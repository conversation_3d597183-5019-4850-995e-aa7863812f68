import os
import torch
import torch.nn as nn
import numpy as np
import pandas as pd
import argparse
import yaml
import json
from datetime import datetime

from models.baseline.lstm import LSTMModelForecaster
from models.baseline.neural_cde import NeuralCDEForecaster
from models.multimodal.image_models import DICOMSliceEncoder
from models.realdiffusionnet import RealDiffFusionNetForecaster
from utils.data_preprocessing import create_dataloaders
from utils.visualization import plot_disease_progression

def load_model(model_path, model_type, input_dim, config):
    """
    加载预训练模型
    
    参数:
    model_path: 模型文件路径
    model_type: 模型类型
    input_dim: 输入特征维度
    config: 配置字典
    
    返回:
    加载的模型
    """
    model = None
    
    # 创建模型实例
    if model_type == 'lstm':
        model = LSTMModelForecaster(
            input_dim=input_dim,
            hidden_dim=config.get('hidden_dim', 128),
            num_layers=config.get('num_layers', 2),
            output_dim=1,
            dropout=config.get('dropout', 0.2)
        )
    elif model_type == 'neural_cde':
        model = NeuralCDEForecaster(
            input_channels=input_dim,
            hidden_channels=config.get('hidden_dim', 128),
            output_channels=1,
            interpolation=config.get('interpolation', 'cubic')
        )
    elif model_type == 'realdiffusionnet':
        # 确定图像嵌入维度
        image_embed_dim = config.get('image_embed_dim', 128)
        if not config.get('use_multimodal', False):
            image_embed_dim = 0
            
        model = RealDiffFusionNetForecaster(
            input_channels=input_dim,
            hidden_channels=config.get('hidden_dim', 128),
            image_embed_dim=image_embed_dim,
            output_dim=1,
            attention_dim=config.get('attention_dim', 128),
            num_heads=config.get('num_heads', 4),
            ff_dim=config.get('ff_dim', 256),
            num_layers=config.get('num_layers', 2),
            dropout=config.get('dropout', 0.1),
            fusion_type=config.get('fusion_type', 'concat'),
            interpolation=config.get('interpolation', 'rectilinear')
        )
    else:
        raise ValueError(f"Unknown model type: {model_type}")
    
    # 加载模型参数
    if os.path.exists(model_path):
        checkpoint = torch.load(model_path, map_location=torch.device('cpu'), weights_only=False)
        model.load_state_dict(checkpoint['model_state_dict'])
        print(f"Loaded model from {model_path}")
    else:
        raise FileNotFoundError(f"Model file not found: {model_path}")
    
    return model

def predict_patient(model, patient_data, config, output_dir, device):
    """
    预测特定患者的疾病进展
    
    参数:
    model: 预训练模型
    patient_data: 患者数据，包含特征和图像
    config: 配置字典
    output_dir: 输出目录
    device: 计算设备
    
    返回:
    预测结果
    """
    model.to(device)
    model.eval()
    
    # 提取患者ID
    patient_id = patient_data['patient_id'][0]
    
    with torch.no_grad():
        # 获取患者的特征序列
        features = patient_data['features'].to(device)
        
        # 如果有图像数据
        if 'image' in patient_data and config.get('use_multimodal', False):
            images = patient_data['image'].to(device)
            outputs = model(features, images=images)
        else:
            outputs = model(features)
        
        # 如果输出是3D的，转换为2D
        if outputs.dim() > 2:
            outputs = outputs.squeeze(0)  # [seq_len, output_dim]
        
        # 准备可视化数据
        weeks = patient_data['features'][0, :, 5].cpu().numpy()  # 假设周数在索引5
        actual_fvc = patient_data['features'][0, :, 3].cpu().numpy()  # 假设FVC在索引3
        predicted_fvc = outputs[:, 0].cpu().numpy()
        
        # 将预测值扩展到实际值的长度
        if len(predicted_fvc) < len(actual_fvc):
            # 将剩余时间点的FVC预测为NaN
            predicted_fvc = np.concatenate([predicted_fvc, np.full(len(actual_fvc) - len(predicted_fvc), np.nan)])
        
        # 准备历史和预测数据字典
        history_data = {
            'weeks': weeks,
            'fvc': actual_fvc
        }
        
        prediction_data = {
            'weeks': weeks,
            'fvc': predicted_fvc
        }
        
        # 绘制疾病进展曲线
        plot_disease_progression(
            history_data,
            prediction_data,
            title=f"Patient {patient_id} FVC Progression",
            save_path=os.path.join(output_dir, f"patient_{patient_id}_progression.png")
        )
        
        # 创建结果字典
        result = {
            'patient_id': patient_id,
            'weeks': weeks.tolist(),
            'actual_fvc': actual_fvc.tolist(),
            'predicted_fvc': predicted_fvc.tolist()
        }
        
        return result

def predict_future(model, patient_data, future_weeks, config, device):
    """
    预测患者未来的疾病进展
    
    参数:
    model: 预训练模型
    patient_data: 患者数据，包含特征和图像
    future_weeks: 需要预测的未来周数列表
    config: 配置字典
    device: 计算设备
    
    返回:
    未来预测结果
    """
    model.to(device)
    model.eval()
    
    # 提取患者ID
    patient_id = patient_data['patient_id'][0]
    
    with torch.no_grad():
        # 获取患者的特征序列
        features = patient_data['features'].to(device)
        
        # 获取最后一个时间点的特征
        last_features = features[:, -1:, :].clone()
        
        # 准备未来时间点的预测结果
        future_results = []
        
        # 如果有图像数据
        if 'image' in patient_data and config.get('use_multimodal', False):
            images = patient_data['image'].to(device)
            
            # 对每个未来时间点进行预测
            for week in future_weeks:
                # 更新时间特征（假设周数在索引5）
                future_feature = last_features.clone()
                future_feature[0, 0, 5] = week
                
                # 拼接历史特征和未来特征
                input_features = torch.cat([features, future_feature], dim=1)
                
                # 预测
                outputs = model(input_features, images=images)
                
                # 获取预测值
                if outputs.dim() > 2:
                    pred_fvc = outputs[0, -1, 0].item()
                else:
                    pred_fvc = outputs[0, 0].item()
                
                future_results.append({
                    'week': week,
                    'predicted_fvc': pred_fvc
                })
        else:
            # 对每个未来时间点进行预测
            for week in future_weeks:
                # 更新时间特征（假设周数在索引5）
                future_feature = last_features.clone()
                future_feature[0, 0, 5] = week
                
                # 拼接历史特征和未来特征
                input_features = torch.cat([features, future_feature], dim=1)
                
                # 预测
                outputs = model(input_features)
                
                # 获取预测值
                if outputs.dim() > 2:
                    pred_fvc = outputs[0, -1, 0].item()
                else:
                    pred_fvc = outputs[0, 0].item()
                
                future_results.append({
                    'week': week,
                    'predicted_fvc': pred_fvc
                })
        
        # 创建结果字典
        result = {
            'patient_id': patient_id,
            'future_predictions': future_results
        }
        
        return result

def main():
    parser = argparse.ArgumentParser(description='使用RealDiffFusionNet模型进行疾病进展预测')
    parser.add_argument('--model_path', type=str, required=True,
                        help='模型文件路径')
    parser.add_argument('--model_type', type=str, default='realdiffusionnet',
                        help='模型类型（lstm, neural_cde, realdiffusionnet）')
    parser.add_argument('--dataset', type=str, default='osic',
                        help='数据集名称（osic或adni）')
    parser.add_argument('--data_path', type=str, required=True,
                        help='数据目录路径')
    parser.add_argument('--config', type=str, default=None,
                        help='配置文件路径')
    parser.add_argument('--output_dir', type=str, default='predictions',
                        help='输出目录')
    parser.add_argument('--patient_id', type=str, default=None,
                        help='要预测的患者ID，如果不提供则预测所有测试患者')
    parser.add_argument('--predict_future', action='store_true',
                        help='是否预测未来时间点')
    parser.add_argument('--future_weeks', type=int, nargs='+', default=[],
                        help='要预测的未来周数列表')
    parser.add_argument('--max_patients', type=int, default=None,
                        help='要预测的最大患者数量')
    
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 加载配置
    config = {}
    if args.config and os.path.exists(args.config):
        with open(args.config, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        print(f"Loaded configuration from {args.config}")
    
    # 更新配置
    config.update(vars(args))
    
    # 创建数据加载器
    _, _, test_loader = create_dataloaders(
        dataset_name=args.dataset,
        data_dir=args.data_path,
        batch_size=1,  # 批大小为1，以便单独处理每个患者
        use_images=config.get('use_multimodal', False),
        max_seq_len=config.get('max_seq_len', None)
    )
    
    if test_loader is None:
        print("Failed to create test dataloader. Exiting.")
        return
    
    # 确定输入特征维度
    sample_batch = next(iter(test_loader))
    input_dim = sample_batch['features'].shape[2]
    
    # 加载模型
    model = load_model(args.model_path, args.model_type, input_dim, config)
    device = torch.device('cuda' if torch.cuda.is_available() and config.get('device', 'cuda') == 'cuda' else 'cpu')
    print(f"Using device: {device}")
    
    # 预测结果
    results = []
    future_results = []
    
    # 如果指定了患者ID
    if args.patient_id:
        patient_found = False
        for batch in test_loader:
            if batch['patient_id'][0] == args.patient_id:
                patient_found = True
                
                # 进行预测
                result = predict_patient(model, batch, config, args.output_dir, device)
                results.append(result)
                print(f"Predicted disease progression for patient {args.patient_id}")
                
                # 如果需要预测未来时间点
                if args.predict_future and args.future_weeks:
                    future_result = predict_future(
                        model, batch, args.future_weeks, config, device
                    )
                    future_results.append(future_result)
                    print(f"Predicted future disease progression for patient {args.patient_id}")
                
                break
        
        if not patient_found:
            print(f"Patient {args.patient_id} not found in test set.")
    else:
        # 预测所有测试患者
        num_patients = 0
        for batch in test_loader:
            # 进行预测
            result = predict_patient(model, batch, config, args.output_dir, device)
            results.append(result)
            print(f"Predicted disease progression for patient {batch['patient_id'][0]}")
            
            # 如果需要预测未来时间点
            if args.predict_future and args.future_weeks:
                future_result = predict_future(
                    model, batch, args.future_weeks, config, device
                )
                future_results.append(future_result)
                print(f"Predicted future disease progression for patient {batch['patient_id'][0]}")
            
            num_patients += 1
            if args.max_patients is not None and num_patients >= args.max_patients:
                break
    
    # 保存预测结果
    if results:
        current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = os.path.join(args.output_dir, f"predictions_{current_time}.json")
        with open(output_path, 'w') as f:
            json.dump(results, f, indent=4)
        print(f"Saved prediction results to {output_path}")
    
    # 保存未来预测结果
    if future_results:
        current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        future_output_path = os.path.join(args.output_dir, f"future_predictions_{current_time}.json")
        with open(future_output_path, 'w') as f:
            json.dump(future_results, f, indent=4)
        print(f"Saved future prediction results to {future_output_path}")
        
        # 转换为CSV格式，更易于分析
        csv_data = []
        for result in future_results:
            patient_id = result['patient_id']
            for pred in result['future_predictions']:
                csv_data.append({
                    'patient_id': patient_id,
                    'week': pred['week'],
                    'predicted_fvc': pred['predicted_fvc']
                })
        
        future_csv_path = os.path.join(args.output_dir, f"future_predictions_{current_time}.csv")
        pd.DataFrame(csv_data).to_csv(future_csv_path, index=False)
        print(f"Saved future prediction results to CSV: {future_csv_path}")

if __name__ == "__main__":
    main() 