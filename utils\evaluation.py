import numpy as np
import torch
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score, accuracy_score, precision_recall_fscore_support
from scipy.stats import pearsonr
import matplotlib.pyplot as plt
import pandas as pd

class MetricsCalculator:
    """
    模型评估指标计算器
    """
    @staticmethod
    def rmse(y_true, y_pred):
        """
        计算均方根误差（Root Mean Squared Error）
        
        参数:
        y_true: 真实值（numpy数组或PyTorch张量）
        y_pred: 预测值（numpy数组或PyTorch张量）
        
        返回:
        RMSE值
        """
        if isinstance(y_true, torch.Tensor):
            y_true = y_true.detach().cpu().numpy()
        if isinstance(y_pred, torch.Tensor):
            y_pred = y_pred.detach().cpu().numpy()
        
        return np.sqrt(mean_squared_error(y_true, y_pred))
    
    @staticmethod
    def mae(y_true, y_pred):
        """
        计算平均绝对误差（Mean Absolute Error）
        
        参数:
        y_true: 真实值（numpy数组或PyTorch张量）
        y_pred: 预测值（numpy数组或PyTorch张量）
        
        返回:
        MAE值
        """
        if isinstance(y_true, torch.Tensor):
            y_true = y_true.detach().cpu().numpy()
        if isinstance(y_pred, torch.Tensor):
            y_pred = y_pred.detach().cpu().numpy()
        
        return mean_absolute_error(y_true, y_pred)
    
    @staticmethod
    def r2(y_true, y_pred):
        """
        计算决定系数（R-squared）
        
        参数:
        y_true: 真实值（numpy数组或PyTorch张量）
        y_pred: 预测值（numpy数组或PyTorch张量）
        
        返回:
        R²值
        """
        if isinstance(y_true, torch.Tensor):
            y_true = y_true.detach().cpu().numpy()
        if isinstance(y_pred, torch.Tensor):
            y_pred = y_pred.detach().cpu().numpy()
        
        return r2_score(y_true, y_pred)
    
    @staticmethod
    def pearson_corr(y_true, y_pred):
        """
        计算皮尔逊相关系数（Pearson Correlation）
        
        参数:
        y_true: 真实值（numpy数组或PyTorch张量）
        y_pred: 预测值（numpy数组或PyTorch张量）
        
        返回:
        相关系数和p值
        """
        if isinstance(y_true, torch.Tensor):
            y_true = y_true.detach().cpu().numpy()
        if isinstance(y_pred, torch.Tensor):
            y_pred = y_pred.detach().cpu().numpy()
        
        # 处理异常情况
        if len(y_true) < 2 or np.all(y_true == y_true[0]) or np.all(y_pred == y_pred[0]):
            return 0.0, 1.0
        
        corr, p_value = pearsonr(y_true, y_pred)
        return corr, p_value
    
    @staticmethod
    def classification_metrics(y_true, y_pred):
        """
        计算分类任务的评估指标
        
        参数:
        y_true: 真实标签（numpy数组或PyTorch张量）
        y_pred: 预测标签（numpy数组或PyTorch张量）
        
        返回:
        包含准确率、精确率、召回率和F1值的字典
        """
        if isinstance(y_true, torch.Tensor):
            y_true = y_true.detach().cpu().numpy()
        if isinstance(y_pred, torch.Tensor):
            if y_pred.dim() > 1 and y_pred.size(1) > 1:
                # 对于多类别分类，获取最大概率的类别索引
                y_pred = torch.argmax(y_pred, dim=1).detach().cpu().numpy()
            else:
                y_pred = y_pred.detach().cpu().numpy()
                
        # 对于二分类，确保预测值为0/1
        if len(np.unique(y_true)) == 2:
            y_pred = (y_pred > 0.5).astype(int)
            
        accuracy = accuracy_score(y_true, y_pred)
        
        # 计算每个类别的精确率、召回率和F1值
        precision, recall, f1, _ = precision_recall_fscore_support(
            y_true, y_pred, average='weighted', zero_division=0
        )
        
        return {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1': f1
        }
    
    @staticmethod
    def calculate_regression_metrics(y_true, y_pred):
        """
        计算回归任务的评估指标
        
        参数:
        y_true: 真实值（numpy数组或PyTorch张量）
        y_pred: 预测值（numpy数组或PyTorch张量）
        
        返回:
        包含RMSE、MAE、R²和相关系数的字典
        """
        rmse = MetricsCalculator.rmse(y_true, y_pred)
        mae = MetricsCalculator.mae(y_true, y_pred)
        r2 = MetricsCalculator.r2(y_true, y_pred)
        corr, p_value = MetricsCalculator.pearson_corr(y_true, y_pred)
        
        return {
            'rmse': rmse,
            'mae': mae,
            'r2': r2,
            'corr': corr,
            'p_value': p_value
        }
    
    @staticmethod
    def calculate_all(y_true, y_pred, task='regression'):
        """
        计算所有评估指标
        
        参数:
        y_true: 真实值（numpy数组或PyTorch张量）
        y_pred: 预测值（numpy数组或PyTorch张量）
        task: 任务类型，'regression'或'classification'
        
        返回:
        包含所有指标的字典
        """
        if task == 'regression':
            return MetricsCalculator.calculate_regression_metrics(y_true, y_pred)
        else:  # classification
            return MetricsCalculator.classification_metrics(y_true, y_pred)

    @staticmethod
    def calculate_classification_metrics(y_true, y_pred, average='weighted'):
        """
        计算分类任务的详细评估指标
        
        参数:
        y_true: 真实标签（numpy数组或PyTorch张量）
        y_pred: 预测标签或概率（numpy数组或PyTorch张量）
        average: 多类别指标的平均方法，可选'micro'、'macro'、'weighted'
        
        返回:
        包含各项分类指标的字典
        """
        if isinstance(y_true, torch.Tensor):
            y_true = y_true.detach().cpu().numpy()
            
        if isinstance(y_pred, torch.Tensor):
            if y_pred.dim() > 1 and y_pred.size(1) > 1:
                # 多类别分类，获取最大概率的类别索引
                y_pred = torch.argmax(y_pred, dim=1).detach().cpu().numpy()
            else:
                y_pred = y_pred.detach().cpu().numpy()
                
        # 对于二分类，确保预测值为0/1
        if len(np.unique(y_true)) == 2:
            y_pred = (y_pred > 0.5).astype(int)
            
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
        from sklearn.metrics import confusion_matrix, roc_auc_score, cohen_kappa_score
        from sklearn.metrics import classification_report
        
        # 基本指标计算
        accuracy = accuracy_score(y_true, y_pred)
        precision = precision_score(y_true, y_pred, average=average, zero_division=0)
        recall = recall_score(y_true, y_pred, average=average, zero_division=0)
        f1 = f1_score(y_true, y_pred, average=average, zero_division=0)
        
        # 混淆矩阵
        cm = confusion_matrix(y_true, y_pred)
        
        # 计算Cohen's Kappa系数（衡量分类器与随机分类的差异）
        kappa = cohen_kappa_score(y_true, y_pred)
        
        # 获取类别数量
        n_classes = len(np.unique(np.concatenate([y_true, y_pred])))
        
        # 计算AUC-ROC（如果是多类别，则使用one-vs-rest方法）
        try:
            # 检查是否有概率输出可用于计算AUC
            if isinstance(y_pred, np.ndarray) and len(y_pred.shape) == 1:
                # 如果y_pred已经是类别而不是概率，暂不计算AUC
                auc = None
            else:
                if n_classes == 2:
                    # 二分类
                    auc = roc_auc_score(y_true, y_pred)
                else:
                    # 多分类
                    # 需要提供每个类的概率，这里假设y_pred还是原始概率分布
                    auc = roc_auc_score(y_true, y_pred, multi_class='ovr', average=average)
        except ValueError:
            # 如果无法计算AUC（例如某些类别没有样本），则跳过
            auc = None
            
        # 生成详细分类报告
        report = classification_report(y_true, y_pred, output_dict=True)
        
        # 对于ADNI特定指标：计算每个诊断类别的准确率
        # 假设类别为：0=CN（认知正常）, 1=MCI（轻度认知障碍）, 2=AD（阿尔茨海默病）
        class_names = ['CN', 'MCI', 'AD'] if n_classes == 3 else [str(i) for i in range(n_classes)]
        class_accuracy = {}
        for i in range(n_classes):
            if i in np.unique(y_true):  # 确保该类别存在样本
                mask = (y_true == i)
                if np.any(mask):  # 避免除以零
                    class_accuracy[class_names[i]] = accuracy_score(
                        y_true[mask], y_pred[mask]
                    )
        
        # 综合各项指标到一个字典中
        metrics = {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1': f1,
            'kappa': kappa,
            'class_accuracy': class_accuracy,
        }
        
        # 添加AUC（如果计算成功）
        if auc is not None:
            metrics['auc'] = auc
            
        return metrics

class EvaluationTracker:
    """
    训练和评估过程中的指标追踪器
    """
    def __init__(self, task='regression'):
        self.task = task
        self.reset()
    
    def reset(self):
        """重置所有指标"""
        self.train_losses = []
        self.val_losses = []
        self.test_losses = []
        self.train_metrics = []
        self.val_metrics = []
        self.test_metrics = []
        
        # 根据任务类型设置最佳指标
        if self.task == 'regression':
            self.best_val_metric = float('inf')  # 对于回归任务，最小化RMSE
            self.best_metric_name = 'rmse'
            self.is_better = lambda new, old: new < old  # 较小的RMSE更好
        else:  # classification
            self.best_val_metric = 0.0  # 对于分类任务，最大化准确率
            self.best_metric_name = 'accuracy'
            self.is_better = lambda new, old: new > old  # 较大的准确率更好
            
        self.best_epoch = -1
        self.patient_predictions = {}  # 按患者ID存储预测结果
    
    def update_train(self, loss, y_true, y_pred):
        """
        更新训练指标
        
        参数:
        loss: 当前批次的损失值
        y_true: 真实值
        y_pred: 预测值
        """
        self.train_losses.append(loss)
        metrics = MetricsCalculator.calculate_all(y_true, y_pred, self.task)
        self.train_metrics.append(metrics)
        
        return metrics
    
    def update_val(self, loss, y_true, y_pred, patient_ids=None, epoch=None):
        """
        更新验证指标
        
        参数:
        loss: 当前批次的损失值
        y_true: 真实值
        y_pred: 预测值
        patient_ids: 患者ID列表
        epoch: 当前轮次
        
        返回:
        是否为最佳模型
        """
        self.val_losses.append(loss)
        metrics = MetricsCalculator.calculate_all(y_true, y_pred, self.task)
        self.val_metrics.append(metrics)
        
        # 存储每个患者的预测结果
        if patient_ids is not None:
            for i, pid in enumerate(patient_ids):
                if pid not in self.patient_predictions:
                    self.patient_predictions[pid] = {'true': [], 'pred': []}
                
                true_value = y_true[i].item() if isinstance(y_true[i], torch.Tensor) else y_true[i]
                pred_value = y_pred[i].item() if isinstance(y_pred[i], torch.Tensor) else y_pred[i]
                
                self.patient_predictions[pid]['true'].append(true_value)
                self.patient_predictions[pid]['pred'].append(pred_value)
        
        # 检查是否为最佳模型
        is_best = False
        current_metric = metrics[self.best_metric_name]
        if self.is_better(current_metric, self.best_val_metric):
            self.best_val_metric = current_metric
            self.best_epoch = epoch if epoch is not None else len(self.val_metrics) - 1
            is_best = True
        
        return metrics, is_best
    
    def update_test(self, loss, y_true, y_pred, patient_ids=None, time_points=None):
        """
        更新测试指标
        
        参数:
        loss: 当前批次的损失值
        y_true: 真实值
        y_pred: 预测值
        patient_ids: 患者ID列表
        time_points: 时间点列表
        """
        self.test_losses.append(loss)
        metrics = MetricsCalculator.calculate_all(y_true, y_pred, self.task)
        self.test_metrics.append(metrics)
        
        # 存储每个患者的预测结果，包括时间点
        if patient_ids is not None:
            for i, pid in enumerate(patient_ids):
                if pid not in self.patient_predictions:
                    self.patient_predictions[pid] = {'true': [], 'pred': [], 'time': []}
                
                true_value = y_true[i].item() if isinstance(y_true[i], torch.Tensor) else y_true[i]
                pred_value = y_pred[i].item() if isinstance(y_pred[i], torch.Tensor) else y_pred[i]
                
                self.patient_predictions[pid]['true'].append(true_value)
                self.patient_predictions[pid]['pred'].append(pred_value)
                
                # 如果提供了时间点，也存储
                if time_points is not None:
                    time_point = time_points[i].item() if isinstance(time_points[i], torch.Tensor) else time_points[i]
                    if 'time' not in self.patient_predictions[pid]:
                        self.patient_predictions[pid]['time'] = []
                    self.patient_predictions[pid]['time'].append(time_point)
        
        return metrics
    
    def get_epoch_metrics(self, epoch, phase='train'):
        """
        获取特定轮次的指标
        
        参数:
        epoch: 轮次索引
        phase: 阶段，'train'、'val'或'test'
        
        返回:
        指定轮次的指标
        """
        if phase == 'train':
            metrics_list = self.train_metrics
        elif phase == 'val':
            metrics_list = self.val_metrics
        elif phase == 'test':
            metrics_list = self.test_metrics
        else:
            raise ValueError(f"Unknown phase: {phase}")
        
        if 0 <= epoch < len(metrics_list):
            return metrics_list[epoch]
        else:
            raise IndexError(f"Epoch {epoch} out of range for {phase} metrics")
    
    def get_best_val_metrics(self):
        """
        获取最佳验证指标
        
        返回:
        最佳验证指标
        """
        if self.best_epoch >= 0:
            return self.val_metrics[self.best_epoch]
        else:
            return None
    
    def summarize(self):
        """
        汇总所有指标
        
        返回:
        包含所有阶段平均指标的字典
        """
        # 计算每个阶段的平均指标
        train_summary = self._average_metrics(self.train_metrics)
        val_summary = self._average_metrics(self.val_metrics)
        test_summary = self._average_metrics(self.test_metrics)
        
        return {
            'train': train_summary,
            'val': val_summary,
            'test': test_summary,
            'best_val': self.get_best_val_metrics(),
            'best_epoch': self.best_epoch
        }
    
    def _average_metrics(self, metrics_list):
        """计算指标列表的平均值"""
        if not metrics_list:
            return None
        
        # 初始化汇总字典
        summary = {key: 0.0 for key in metrics_list[0].keys()}
        
        # 累加所有指标
        for metrics in metrics_list:
            for key, value in metrics.items():
                summary[key] += value
        
        # 计算平均值
        for key in summary.keys():
            summary[key] /= len(metrics_list)
        
        return summary
    
    def evaluate_future_predictions(self, horizon_steps=3):
        """
        评估预测未来时间点的性能
        
        参数:
        horizon_steps: 要评估的未来时间步数
        
        返回:
        每个时间步的评估指标
        """
        horizon_metrics = []
        
        # 对于每个预测时间步
        for h in range(1, horizon_steps + 1):
            h_true = []
            h_pred = []
            
            # 收集所有患者指定时间步的预测和真值
            for pid, data in self.patient_predictions.items():
                true_values = data['true']
                pred_values = data['pred']
                
                # 只考虑有足够长序列的患者
                if len(true_values) > h:
                    # 获取每h步的预测和真值对
                    for i in range(0, len(true_values) - h):
                        # 将预测值与h步后的真值进行比较
                        h_true.append(true_values[i + h])
                        h_pred.append(pred_values[i])
            
            # 如果有足够的数据点
            if len(h_true) > 0 and len(h_pred) > 0:
                # 计算该时间步的指标
                h_metrics = MetricsCalculator.calculate_all(
                    np.array(h_true), 
                    np.array(h_pred),
                    self.task
                )
                h_metrics['horizon'] = h
                horizon_metrics.append(h_metrics)
        
        return horizon_metrics
    
    def plot_patient_trajectories(self, patient_ids=None, save_path=None):
        """
        绘制患者轨迹图
        
        参数:
        patient_ids: 要绘制的患者ID列表，如果为None则绘制所有患者
        save_path: 保存图表的路径，如果为None则显示图表
        
        返回:
        None
        """
        # 如果未指定患者ID，则使用所有患者
        if patient_ids is None:
            patient_ids = list(self.patient_predictions.keys())
        
        # 限制绘图的患者数量，避免图表过于拥挤
        max_patients = min(9, len(patient_ids))
        patient_ids = patient_ids[:max_patients]
        
        # 创建子图
        fig, axes = plt.subplots(3, 3, figsize=(15, 15))
        axes = axes.flatten()
        
        # 对每个患者绘制轨迹
        for i, pid in enumerate(patient_ids):
            if i >= len(axes):
                break
                
            if pid in self.patient_predictions:
                true_values = self.patient_predictions[pid]['true']
                pred_values = self.patient_predictions[pid]['pred']
                
                # 使用时间点（如果有）或序号
                if 'time' in self.patient_predictions[pid]:
                    time_points = self.patient_predictions[pid]['time']
                else:
                    time_points = list(range(len(true_values)))
                
                # 绘制真实和预测值
                axes[i].plot(time_points, true_values, 'b-', label='True')
                axes[i].plot(time_points, pred_values, 'r--', label='Predicted')
                axes[i].set_title(f'Patient {pid}')
                axes[i].set_xlabel('Time')
                
                if self.task == 'regression':
                    if 'FVC' in self.patient_predictions:
                        axes[i].set_ylabel('FVC')
                    elif 'MMSE' in self.patient_predictions:
                        axes[i].set_ylabel('MMSE Score')
                    else:
                        axes[i].set_ylabel('Value')
                else:
                    axes[i].set_ylabel('Class')
                
                axes[i].legend()
        
        # 隐藏未使用的子图
        for j in range(i + 1, len(axes)):
            axes[j].axis('off')
        
        plt.tight_layout()
        
        # 保存或显示图表
        if save_path:
            plt.savefig(save_path)
        else:
            plt.show()

def evaluate_model(model, dataloader, criterion, device, task='regression', compute_metrics=True, return_predictions=False):
    """
    评估模型性能
    
    参数:
    model: 要评估的模型
    dataloader: 数据加载器
    criterion: 损失函数
    device: 设备（CPU或GPU）
    task: 任务类型，'regression'或'classification'
    compute_metrics: 是否计算评估指标
    return_predictions: 是否返回预测结果
    
    返回:
    损失、评估指标和预测结果（如果return_predictions为True）
    """
    model.eval()
    total_loss = 0.0
    all_targets = []
    all_predictions = []
    all_patient_ids = []
    
    with torch.no_grad():
        for batch in dataloader:
            features = batch['features'].to(device)
            targets = batch['target'].to(device)
            
            # 收集患者ID（如果可用）
            if 'patient_id' in batch:
                all_patient_ids.extend(batch['patient_id'])
            
            # 处理不同类型的模型调用
            lengths = batch.get('lengths', None)
            if lengths is not None:
                lengths = lengths.to(device)

            # 如果有图像数据且模型支持多模态
            if 'image' in batch:
                images = batch['image'].to(device)
                try:
                    # 尝试调用支持图像的模型
                    outputs = model(features, lengths=lengths, images=images)
                except TypeError:
                    # 如果模型不支持images参数，则只使用features
                    try:
                        outputs = model(features, lengths=lengths)
                    except TypeError:
                        # 如果模型也不支持lengths参数
                        outputs = model(features)
            else:
                try:
                    outputs = model(features, lengths=lengths)
                except TypeError:
                    # 如果模型不支持lengths参数
                    outputs = model(features)
            
            # 获取最后一个时间步的输出（对于序列预测模型）
            if outputs.dim() > 2:
                outputs = outputs[:, -1, :]

            # 确保输出维度匹配目标维度
            if outputs.dim() > 1 and outputs.size(1) == 1:
                outputs = outputs.squeeze(1)
            
            # 对于分类任务，可能需要额外处理
            if task == 'classification' and outputs.shape[-1] > 1:
                # 多类别分类，损失函数期望类别索引
                loss = criterion(outputs, targets.long())
                # 使用softmax获取概率并找到最大概率的类
                predictions = torch.softmax(outputs, dim=1)
            else:
                # 回归或二分类任务
                loss = criterion(outputs, targets)
                predictions = outputs
            
            total_loss += loss.item() * features.size(0)
            
            # 收集预测和目标值
            all_targets.append(targets.cpu().numpy())
            all_predictions.append(predictions.cpu().numpy())
    
    # 计算平均损失
    avg_loss = total_loss / len(dataloader.dataset)
    
    # 合并所有批次的预测和目标值
    all_targets = np.concatenate(all_targets)
    all_predictions = np.concatenate(all_predictions)

    # 确保预测和目标的形状匹配
    if all_predictions.ndim > 1 and all_predictions.shape[1] == 1:
        all_predictions = all_predictions.squeeze(1)
    if all_targets.ndim > 1 and all_targets.shape[1] == 1:
        all_targets = all_targets.squeeze(1)

    # 确保长度匹配
    min_len = min(len(all_targets), len(all_predictions))
    all_targets = all_targets[:min_len]
    all_predictions = all_predictions[:min_len]
    
    # 如果需要计算评估指标
    if compute_metrics:
        metrics = MetricsCalculator.calculate_all(all_targets, all_predictions, task)
    else:
        metrics = {}
    
    # 如果需要返回预测结果
    if return_predictions:
        return avg_loss, metrics, all_targets, all_predictions, all_patient_ids
    
    return avg_loss, metrics

def evaluate_future_predictions(model, dataloader, criterion, device, horizon_steps=3, task='regression'):
    """
    评估模型在预测未来时间点上的性能
    
    参数:
    model: 要评估的模型
    dataloader: 数据加载器
    criterion: 损失函数
    device: 设备（CPU或GPU）
    horizon_steps: 要评估的未来时间步数
    task: 任务类型，'regression'或'classification'
    
    返回:
    每个时间步的评估指标
    """
    model.eval()
    
    # 按患者分组的数据
    patient_data = {}
    
    # 首先收集所有患者数据
    with torch.no_grad():
        for batch in dataloader:
            features = batch['features']
            targets = batch['target'].cpu().numpy()
            patient_ids = batch['patient_id']
            
            # 按患者分组
            for i, pid in enumerate(patient_ids):
                if pid not in patient_data:
                    patient_data[pid] = {'features': [], 'targets': []}
                
                patient_data[pid]['features'].append(features[i].cpu().numpy())
                patient_data[pid]['targets'].append(targets[i])
    
    # 对每个时间步进行预测和评估
    horizon_metrics = []
    
    for h in range(1, horizon_steps + 1):
        h_true = []
        h_pred = []
        
        # 对每个患者分别处理
        for pid, data in patient_data.items():
            features_seq = data['features']
            targets_seq = data['targets']
            
            # 只考虑有足够长序列的患者
            if len(features_seq) > h:
                # 对每个可用的时间步进行预测
                for i in range(len(features_seq) - h):
                    # 使用到i时刻的特征来预测i+h时刻的目标
                    features_batch = torch.tensor(features_seq[:(i+1)], dtype=torch.float32).unsqueeze(0).to(device)
                    
                    # 执行预测
                    with torch.no_grad():
                        try:
                            if 'image' in batch:
                                # 这里假设我们有图像特征，但需要根据实际情况调整
                                images = batch['image'][i:i+1].to(device)
                                output = model(features_batch, images=images)
                            else:
                                output = model(features_batch)
                        except TypeError:
                            # 如果模型不支持images参数，则只使用features
                            output = model(features_batch)
                    
                    # 获取预测结果
                    prediction = output[0, -1].cpu().numpy()
                    
                    # 与真实值对比
                    true_value = targets_seq[i + h]
                    
                    h_true.append(true_value)
                    h_pred.append(prediction)
        
        # 计算该时间步的评估指标
        if len(h_true) > 0 and len(h_pred) > 0:
            h_metrics = MetricsCalculator.calculate_all(np.array(h_true), np.array(h_pred), task)
            h_metrics['horizon'] = h
            horizon_metrics.append(h_metrics)
    
    return horizon_metrics 