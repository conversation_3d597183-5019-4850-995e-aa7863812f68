import torch
import torch.nn as nn
import torchcde

class RectilinearInterpolation:
    """
    直线插值，用于处理不规则采样的时间序列数据
    
    直线插值相比于立方样条插值，具有以下特点：
    1. 连续在线（可以在任何时间评估）vs 离散在线（仅在有新数据时评估）
    2. 允许在无数据点的区域进行外推
    3. 可以看作是ODE-RNN时间序列建模方法的一种泛化
    """
    
    @staticmethod
    def interpolate(x, times=None, rectilinear=True):
        """
        对输入数据进行直线插值
        
        参数:
        x (tensor): 输入数据，形状为 [batch_size, seq_len, channels]
        times (tensor): 时间点，形状为 [seq_len]，如果为None则使用等间隔时间点
        rectilinear (bool): 是否使用直线插值，默认为True
        
        返回:
        coeffs: 插值系数，可用于torchcde的LinearInterpolation
        """
        batch_size, seq_len, channels = x.size()
        
        # 如果未提供时间点，则使用等间隔时间点
        if times is None:
            times = torch.linspace(0, 1, seq_len).to(x.device)

        # 计算线性插值系数
        coeffs = torchcde.linear_interpolation_coeffs(x, times, rectilinear=rectilinear)
        
        return coeffs
    
    @staticmethod
    def get_interpolator(coeffs):
        """
        获取基于系数的插值器对象
        
        参数:
        coeffs: 由interpolate方法生成的系数
        
        返回:
        插值器: LinearInterpolation对象，可用于在任意时间点评估和求导
        """
        return torchcde.LinearInterpolation(coeffs)
    
    @staticmethod
    def evaluate(interpolator, t):
        """
        在给定时间点评估插值
        
        参数:
        interpolator: 插值器对象
        t: 要评估的时间点
        
        返回:
        插值值: 在时间点t的插值
        """
        return interpolator.evaluate(t)
    
    @staticmethod
    def derivative(interpolator, t):
        """
        在给定时间点计算导数
        
        参数:
        interpolator: 插值器对象
        t: 要计算导数的时间点
        
        返回:
        导数值: 在时间点t的导数
        """
        return interpolator.derivative(t) 