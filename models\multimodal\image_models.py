import torch
import torch.nn as nn
from efficientnet_pytorch import EfficientNet
import torchvision.transforms as transforms

class ImageEncoder(nn.Module):
    """
    图像编码器，使用预训练的EfficientNet-b0模型
    
    参数:
    output_dim (int): 输出特征维度，默认128
    pretrained (bool): 是否使用预训练权重，默认True
    """
    def __init__(self, output_dim=128, pretrained=True):
        super(ImageEncoder, self).__init__()
        
        # 加载预训练的EfficientNet-b0
        if pretrained:
            self.efficientnet = EfficientNet.from_pretrained('efficientnet-b0')
        else:
            self.efficientnet = EfficientNet.from_name('efficientnet-b0')
        
        # 提取特征维度
        self.feature_dim = self.efficientnet._fc.in_features
        
        # 替换分类层为投影层
        self.efficientnet._fc = nn.Identity()
        
        # 投影层，将EfficientNet特征映射到指定维度
        self.projection = nn.Sequential(
            nn.Linear(self.feature_dim, 512),
            nn.ReLU(inplace=True),
            nn.Dropout(0.2),
            nn.Linear(512, output_dim)
        )
        
        # 定义图像预处理变换
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
    
    def forward(self, x):
        """
        前向传播
        
        参数:
        x: 输入图像，形状为 [batch_size, channels, height, width]
        
        返回:
        图像特征: 形状为 [batch_size, output_dim]
        """
        # 应用图像变换
        x = self.transform(x)
        
        # 使用EfficientNet提取特征
        features = self.efficientnet(x)
        
        # 应用投影层
        embedding = self.projection(features)
        
        return embedding

class DICOMSliceEncoder(nn.Module):
    """
    DICOM切片编码器，专为医学成像设计
    
    参数:
    output_dim (int): 输出特征维度，默认128
    pretrained (bool): 是否使用预训练权重，默认True
    num_slices (int): 要处理的切片数量，默认1
    """
    def __init__(self, output_dim=128, pretrained=True, num_slices=1):
        super(DICOMSliceEncoder, self).__init__()
        
        self.num_slices = num_slices
        
        # 基础图像编码器
        self.image_encoder = ImageEncoder(output_dim, pretrained)
        
        # 如果处理多个切片，添加一个聚合层
        if num_slices > 1:
            self.aggregation = nn.Sequential(
                nn.Linear(output_dim * num_slices, output_dim),
                nn.ReLU(inplace=True),
                nn.Dropout(0.2)
            )
    
    def forward(self, x):
        """
        前向传播
        
        参数:
        x: 输入DICOM切片，形状为 [batch_size, num_slices, channels, height, width]
           或 [batch_size, channels, height, width] 如果 num_slices=1
        
        返回:
        特征: 形状为 [batch_size, output_dim]
        """
        batch_size = x.size(0)
        
        if self.num_slices > 1:
            # 处理多个切片
            slice_features = []
            
            for i in range(self.num_slices):
                slice_i = x[:, i]  # [batch_size, channels, height, width]
                features_i = self.image_encoder(slice_i)
                slice_features.append(features_i)
            
            # 连接所有切片的特征
            combined_features = torch.cat(slice_features, dim=1)
            
            # 聚合特征
            features = self.aggregation(combined_features)
        else:
            # 处理单个切片
            if len(x.shape) == 5:
                x = x.squeeze(1)  # 移除切片维度
            features = self.image_encoder(x)
        
        return features 