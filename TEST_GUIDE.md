# RealDiffFusionNet 测试指南

本指南将帮助您使用测试数据快速开始训练和评估 RealDiffFusionNet 模型。

## 1. 环境准备

首先，确保您已经安装了所有必要的依赖项：

```bash
# 切换到项目根目录
cd /path/to/RealDiffFusionNet

# 安装依赖（使用国内镜像源）
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
```

如果您有 NVIDIA GPU，请安装 GPU 版本的 PyTorch：

```bash
# 针对 CUDA 11.7
pip install torch==1.13.1+cu117 torchvision==0.14.1+cu117 --extra-index-url https://download.pytorch.org/whl/cu117
```

## 2. 验证环境和数据

运行测试脚本，确认环境和测试数据已正确设置：

```bash
python test_setup.py
```

如果一切正常，您应该看到有关 GPU 可用性和数据加载成功的信息。

## 3. 训练模型

### OSIC 数据集（肺纤维化）

使用以下命令在 OSIC 数据集上训练 RealDiffFusionNet 模型：

```bash
python train.py --config configs/realdiffusionnet.yaml --dataset osic --data_path data/osic
```

### ADNI 数据集（阿尔茨海默病）

#### 回归任务（预测 MMSE 评分）

```bash
python train.py --config configs/adni.yaml --task regression --target_column MMSE --data_path data/adni
```

#### 分类任务（预测疾病状态）

```bash
python train.py --config configs/adni.yaml --task classification --target_column DX --data_path data/adni
```

## 4. 评估模型

训练完成后，您可以评估模型性能：

```bash
# 评估 OSIC 数据集模型
python evaluate.py --config configs/realdiffusionnet.yaml --checkpoint output/realdiffusionnet_osic.pt --dataset osic --data_path data/osic

# 评估 ADNI 数据集模型
python evaluate.py --config configs/adni.yaml --checkpoint output/realdiffusionnet_adni.pt --dataset adni --data_path data/adni
```

## 5. 生成预测

使用训练好的模型进行预测：

```bash
python predict.py --config configs/realdiffusionnet.yaml --checkpoint output/realdiffusionnet_osic.pt --input data/osic/test.csv
```

## 6. 常见问题解决

### 内存不足错误

如果遇到内存不足错误，可以尝试以下方法：

1. 减小批量大小：修改配置文件中的 `batch_size` 参数
2. 启用混合精度训练：确保配置文件中 `use_amp` 设置为 `true`
3. 限制序列长度：设置配置文件中的 `max_seq_len` 参数（例如设为 10）

### 图像相关错误

测试数据不包含实际的 CT/MRI 图像。如果您看到与图像相关的错误，请确保配置文件中的 `use_images` 设置为 `false`。

## 7. 参数调整

您可以通过修改配置文件来调整模型参数：

- `hidden_dim`：隐藏层维度
- `num_layers`：模型层数
- `dropout`：丢弃率
- `lr`：学习率
- `num_epochs`：训练轮数

例如，对于资源受限的环境，可以使用以下配置：

```yaml
hidden_dim: 64
num_layers: 1
batch_size: 16
num_epochs: 50
use_amp: true
max_seq_len: 10
```

## 8. 后续步骤

成功运行测试数据后，您可以：

1. 准备更大规模的真实数据集
2. 添加实际的医学图像（CT/MRI）
3. 调整模型参数以获得更好的性能
4. 尝试不同的融合策略（`fusion_type`）

祝您使用愉快！ 