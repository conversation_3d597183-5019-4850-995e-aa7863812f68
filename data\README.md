# 测试数据说明

本目录包含用于测试RealDiffFusionNet模型的简化数据集。

## 数据结构

```
data/
├── osic/
│   ├── train.csv     # 训练数据，包含患者ID、周数、FVC值等信息
│   ├── test.csv      # 测试数据
│   └── train/        # 此目录应包含患者CT扫描图像
├── adni/
│   ├── ADNIMERGE.csv # 合并数据，包含患者ID、访问代码、MMSE评分等信息
│   └── MRI/          # 此目录应包含患者MRI扫描图像
└── README.md         # 本文件
```

## 关于图像数据

由于DICOM文件需要特殊的医学影像格式，本测试数据集不包含实际的CT或MRI扫描。如果您需要完整测试图像处理功能，您可以：

1. 将`configs/realdiffusionnet.yaml`和`configs/adni.yaml`中的`use_images`设置为`false`
2. 或者准备自己的DICOM文件，并按以下结构放置：

### OSIC数据集图像结构
```
data/osic/train/ID00001/*.dcm
data/osic/train/ID00002/*.dcm
data/osic/train/ID00003/*.dcm
```

### ADNI数据集图像结构
```
data/adni/MRI/1_bl/*.nii
data/adni/MRI/1_m06/*.nii
data/adni/MRI/2_bl/*.nii
...
```

## 使用方法

使用这些测试数据运行模型：

```bash
# 训练OSIC数据集模型（不使用图像）
python train.py --config configs/realdiffusionnet.yaml --dataset osic --data_path data/osic

# 训练ADNI数据集回归模型
python train.py --config configs/adni.yaml --task regression --target_column MMSE --data_path data/adni
``` 