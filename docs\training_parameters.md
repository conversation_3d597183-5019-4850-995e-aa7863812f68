# RealDiffusionNet 训练参数说明文档

本文档详细解释了RealDiffFusionNet框架中的训练参数和配置选项。

## 配置文件

配置文件位于`configs`目录下，包括以下几个主要文件：

- `realdiffusionnet.yaml`: RealDiffusionNet主模型配置
- `neural_cde.yaml`: Neural CDE基线模型配置
- `lstm.yaml`: LSTM基线模型配置
- `adni.yaml`: ADNI数据集专用配置

## 模型配置参数

### 通用参数

| 参数 | 描述 | 默认值 | 可选值 |
|------|------|--------|--------|
| `model_type` | 模型类型 | `realdiffusionnet` | `realdiffusionnet`, `neural_cde`, `lstm` |
| `hidden_dim` | 隐藏层维度 | 128 | 任意正整数 |
| `input_dim` | 输入特征维度 | `null` (自动设置) | 任意正整数 |
| `output_dim` | 输出维度 | `null` (自动设置) | 任意正整数 |
| `dropout` | Dropout率 | 0.1 | 0.0-1.0之间 |
| `use_multimodal` | 是否使用多模态融合 | `true` | `true`, `false` |
| `fusion_type` | 融合策略类型 | `concat` | `concat`, `sum` |

### RealDiffusionNet 特定参数

| 参数 | 描述 | 默认值 | 可选值 |
|------|------|--------|--------|
| `attention_dim` | 注意力层维度 | 128 | 任意正整数 |
| `num_heads` | 注意力头数量 | 4 | 任意正整数 |
| `ff_dim` | 前馈网络维度 | 256 | 任意正整数 |
| `num_layers` | 注意力层数量 | 2 | 任意正整数 |

### Neural CDE 特定参数

| 参数 | 描述 | 默认值 | 可选值 |
|------|------|--------|--------|
| `cde_embedding_dim` | CDE嵌入维度 | 64 | 任意正整数 |
| `num_cde_layers` | CDE层数量 | 2 | 任意正整数 |
| `solver` | 微分方程求解器 | `rk4` | `euler`, `midpoint`, `rk4` |
| `adjoint` | 是否使用伴随方法 | `false` | `true`, `false` |
| `return_sequences` | 是否返回所有时间步的输出 | `true` | `true`, `false` |
| `interpolation` | 插值方法 | `cubic` | `cubic`, `rectilinear` |

### 图像模型配置

| 参数 | 描述 | 默认值 | 可选值 |
|------|------|--------|--------|
| `image_backbone` | 图像骨干网络 | `resnet18` | `resnet18`, `resnet50`, `efficientnet_b0` |
| `image_pretrained` | 是否使用预训练权重 | `true` | `true`, `false` |
| `freeze_backbone` | 是否冻结骨干网络参数 | `true` | `true`, `false` |

## 训练配置参数

| 参数 | 描述 | 默认值 | 可选值 |
|------|------|--------|--------|
| `batch_size` | 批大小 | 32 | 任意正整数 |
| `num_epochs` | 训练轮数 | 100 | 任意正整数 |
| `lr` | 学习率 | 0.001 | 任意正浮点数 |
| `weight_decay` | 权重衰减系数 | 0.0001 | 任意非负浮点数 |
| `patience` | 早停耐心值 | 10 | 任意正整数 |
| `max_seq_len` | 最大序列长度 | `null` (不限制) | `null`或任意正整数 |
| `seed` | 随机种子 | 42 | 任意整数 |
| `use_early_stopping` | 是否使用早停 | `true` | `true`, `false` |

## 数据配置参数

| 参数 | 描述 | 默认值 | 可选值 |
|------|------|--------|--------|
| `dataset` | 数据集名称 | `osic` | `osic`, `adni` |
| `data_path` | 数据集路径 | `data/osic` | 任意有效路径 |
| `use_images` | 是否使用图像数据 | `true` | `true`, `false` |
| `task` | 任务类型（对ADNI） | `regression` | `regression`, `classification` |
| `target_column` | 目标列（对ADNI） | `MMSE` | `MMSE`, `DX`等 |

## 输出配置参数

| 参数 | 描述 | 默认值 | 可选值 |
|------|------|--------|--------|
| `output_dir` | 输出目录 | `output` | 任意有效路径 |
| `use_wandb` | 是否使用Weights & Biases | `false` | `true`, `false` |
| `wandb_project` | W&B项目名称 | `realdiffusionnet` | 任意字符串 |
| `wandb_group` | W&B分组名称 | `adni` | 任意字符串 |

## 评估配置参数

| 参数 | 描述 | 默认值 | 可选值 |
|------|------|--------|--------|
| `eval_horizon_steps` | 预测未来时间点数量 | 4 | 任意正整数 |
| `plot_trajectories` | 是否绘制患者轨迹图 | `true` | `true`, `false` |
| `save_predictions` | 是否保存预测结果 | `true` | `true`, `false` |

## 数据集特定参数

### OSIC 数据集

OSIC肺纤维化数据集用于预测肺功能（FVC）随时间的变化。

- 任务类型：回归（预测FVC值）
- 输入特征：人口统计学特征（年龄、性别等）和肺CT扫描
- 时序数据：多个访问时间点的肺功能测量

### ADNI 数据集

ADNI阿尔兹海默病数据集用于认知能力下降和疾病进展建模。

- 任务类型：回归（预测认知评分）或分类（预测疾病状态）
- 输入特征：人口统计学特征、临床评分和脑MRI扫描
- 目标值：
  - 回归：MMSE（简易精神状态检查量表）、ADAS13（阿尔茨海默病评估量表）等
  - 分类：诊断状态（CN：认知正常、MCI：轻度认知障碍、AD：阿尔茨海默病）

## 使用示例

### 训练OSIC数据集的RealDiffusionNet模型

```bash
python train.py --config configs/realdiffusionnet.yaml --dataset osic --data_path /path/to/osic_data
```

### 训练ADNI数据集的分类模型

```bash
python train.py --config configs/adni.yaml --task classification --target_column DX
```

### 使用Neural CDE基线模型

```bash
python train.py --config configs/neural_cde.yaml --dataset osic
```

### 调整超参数示例

```bash
python train.py --config configs/realdiffusionnet.yaml --hidden_dim 256 --num_heads 8 --lr 0.0005
```

## 最佳实践

1. **数据预处理**：确保正确运行预处理脚本，特别是对图像数据
2. **硬件资源**：对于多模态模型，建议使用具有足够GPU内存的设备
3. **超参数调优**：关注以下超参数对性能的影响：
   - `hidden_dim`：调整模型容量
   - `num_heads`：调整注意力机制的表达能力
   - `fusion_type`：不同融合策略对不同任务可能有不同效果
   - `lr`和`batch_size`：影响训练稳定性和收敛速度
4. **早期停止**：推荐使用早期停止来防止过拟合 